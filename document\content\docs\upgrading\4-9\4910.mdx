---
title: V4.9.10
description: FastGPT V4.9.10 更新说明
---

## 升级指南

重要提示：本次更新会重新构建全文索引，构建期间，全文检索结果会为空，4c16g 700 万组全文索引大致消耗 25 分钟。如需无缝升级，需自行做表同步工程。

### 1. 做好数据备份

### 2. 更新镜像 tag

- 更新 FastGPT 镜像 tag: v4.9.10-fix2
- 更新 FastGPT 商业版镜像 tag: v4.9.10-fix2
- mcp_server 无需更新
- Sandbox 无需更新
- AIProxy 无需更新

## 🚀 新增内容

1. 支持 PG 设置`systemEnv.hnswMaxScanTuples`参数，提高迭代搜索的数据总量。
2. 知识库预处理参数增加 “分块条件”，可控制某些情况下不进行分块处理。
3. 知识库预处理参数增加 “段落优先” 模式，可控制最大段落深度。原“长度优先”模式，不再内嵌段落优先逻辑。
4. 工作流调整为单向接入和接出，支持快速的添加下一步节点。
5. 开放飞书和语雀知识库到社区版。
6. gemini 和 claude 最新模型预设。

## ⚙️ 优化

1. LLM stream调用，默认超时调大。
2. 部分确认交互优化。
3. 纠正原先知识库的“表格数据集”名称，改成“备份导入”。同时支持知识库索引的导出和导入。
4. 工作流知识库引用上限，如果工作流中没有相关 AI 节点，则交互模式改成纯手动输入，并且上限为 1000万。
5. 语音输入，移动端判断逻辑，准确判断是否为手机，而不是小屏。
6. 优化上下文截取算法，至少保证留下一组 Human 信息。

## 🐛 修复

1. 全文检索多知识库时排序得分排序不正确。
2. 流响应捕获 finish_reason 可能不正确。
3. 工具调用模式，未保存思考输出。
4. 知识库 indexSize 参数未生效。
5. 工作流嵌套 2 层后，获取预览引用、上下文不正确。
6. xlsx 转成 Markdown 时候，前面会多出一个空格。
7. 读取 Markdown 文件时，Base64 图片未进行额外抓换保存。
