---
title: 'V4.11.0'
description: 'FastGPT V4.11.0 更新说明'
---

## 升级说明

### 1. 修改环境变量

FastGPT 商业版用户，可以增加评估相关环境变量，并在更新后，在管理端点击一次保存。

```
EVAL_CONCURRENCY=3 # 评估单节点并发数
EVAL_LINE_LIMIT=1000 # 评估文件最大行数
```

### 2. 更新镜像：

- 更新 FastGPT 镜像tag: v4.11.0
- 更新 FastGPT 商业版镜像tag: v4.11.0
- 更新 fastgpt-plugin 镜像 tag: v0.1.5
- mcp_server 无需更新
- Sandbox 无需更新
- AIProxy 无需更新

## 项目调整

1. 移除所有**开源功能**的限制，包括：应用数量和知识库数量上限。
2. 调整 RoadMap，增加`上下文管理`,`AI 生成工作流`,`高级编排 DeBug 调试模式`等计划。
3. 海外版域名将`fastgpt.io`调整成`fastgpt.io`。

## 🚀 新增内容

1. 商业版增加**应用评测(Beta 版)**，可对应用进行有监督评分。
2. 工作流部分节点支持报错捕获分支。
3. 对话页独立 tab 页面UX。
4. 支持 Signoz traces 和 logs 系统追踪。
5. 新增 Gemini2.5, grok4, kimi 模型配置。
6. 模型调用日志增加首字响应时长和请求 IP。

## ⚙️ 优化

1. 优化代码，避免递归造成的内存堆积，尤其在高并发连续的进行知识库预处理时，可显著降低内存消耗。
2. 知识库训练：支持全部重试当前集合异常数据。
3. 工作流 valueTypeFormat，避免数据类型不一致。
4. 知识库列表搜索时，正则未进行特殊词替换。

## 🐛 修复

1. 问题分类和内容提取节点，默认模型无法通过前端校验，导致工作流无法运行和保存发布。

## 🔨 工具更新

1. Markdown 文本转 Docx 和 Xlsx 文件。
