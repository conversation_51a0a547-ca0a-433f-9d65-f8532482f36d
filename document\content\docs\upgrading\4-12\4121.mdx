---
title: 'V4.12.1(包含升级脚本)'
description: 'FastGPT V4.12.1 更新说明'
---

## 更新指南

### 1. 更新镜像：

- 更新 FastGPT 镜像tag: v4.12.1-fix
- 更新 FastGPT 商业版镜像tag: v4.12.1
- 更新 fastgpt-plugin 镜像 tag: v0.1.10
- mcp_server 无需更新
- Sandbox 无需更新
- AIProxy 无需更新

### 2. 执行升级脚本

该脚本仅需商业版用户执行。

从任意终端，发起 1 个 HTTP 请求。其中 `{{rootkey}}` 替换成环境变量里的 `rootkey`；`{{host}}` 替换成**FastGPT 域名**。

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv4121' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

**脚本功能**

1. 将历史对话日志整理成新的日志看板数据。

## 🚀 新增内容

1. Prompt 自动生成和优化。
2. 增加`SIGNOZ_STORE_LEVEL`参数，可以控制 Signoz 日志存储级别。

## ⚙️ 优化

1. 工作流响应优化，主动指定响应值进入历史记录，而不是根据 key 决定。
2. 避免工作流中，变量替换导致的死循环或深度递归风险。
3. 对话日志导出，固定导出对话详情。
4. 分页器 UI 优化。

## 🐛 修复

1. 工具密钥输入，boolean 值无法通过 form 校验。
2. 对话页，pane切换可能导致数据异常。
3. 对话日志看板数据表索引不正确。

## 🔨 工具更新

1. 支持对系统工具单独配置 Tool description，更利于模型理解。
