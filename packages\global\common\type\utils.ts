export type RequireAtLeastOne<T, <PERSON> extends keyof T = keyof T> = Omit<T, Keys> &
  {
    [K in Keys]-?: Required<Pick<T, K>> & Partial<Omit<T, K>>;
  }[Keys];

export type RequireOnlyOne<T, <PERSON> extends keyof T = keyof T> = Omit<T, Keys> &
  {
    [K in Keys]-?: Required<Pick<T, K>> & Partial<Record<Exclude<Keys, K>, undefined>>;
  }[Keys];

export type DeepPartial<T> = {
  [P in keyof T]?: DeepPartial<T[P]>;
};
