---
title: V4.9.6
description: FastGPT V4.9.6 更新说明
---

## 🚀 新增内容

1. 以 MCP 方式对外提供应用调用。
2. 支持以 MCP SSE 协议创建工具。
3. 批量执行节点支持交互节点，可实现每一轮循环都人工参与。
4. 增加工作台二级菜单，合并工具箱。
5. 增加 grok3、GPT4.1、o系列、Gemini2.5 模型系统配置。

## ⚙️ 优化

1. 工作流数据类型转化鲁棒性和兼容性增强。
2. Python sandbox 代码，支持大数据输入。
3. 路径组件支持配置最后一步是否可点击。
4. 知识库工具调用结果，自动补充图片域名。
5. Github action runner 升级成 unbuntu24
6. 去除飞书、公众号等三方渠道，回复时，可能前后多一个换行的问题。
7. 调整分块策略，大表格时，不进行超大块合并，而是独立拆块。
8. Iframe 嵌套组件，内置允许麦克风声明。

## 🐛 修复

1. 修复子工作流包含交互节点时，未成功恢复子工作流所有数据。
2. completion v1 接口，未接受 interactive 参数，导致 API 调用失败。
3. 连续工具调用，上下文截断异常

## 升级指南

### 1. 做好数据备份

### 2. 部署 MCP server 服务

#### Docker 部署

在`docker-compose.yml`文件中，加入`fastgpt-mcp-server`服务:

```yml
fastgpt-mcp-server:
  container_name: fastgpt-mcp-server
  image: ghcr.io/labring/fastgpt-mcp_server:v4.9.6
  ports:
    - 3005:3000
  networks:
    - fastgpt
  restart: always
  environment:
    - FASTGPT_ENDPOINT=http://fastgpt:3000
```

#### Sealos 部署

直接在`应用管理`中，增加一个`fastgpt-mcp-server`应用，镜像为`ghcr.io/labring/fastgpt-mcp_server:v4.9.6`，并设置环境变量`FASTGPT_ENDPOINT=fastgpt 的访问地址`。

### 3. 修改 FastGPT 容器环境变量

#### 社区版

修改`config.json`配置文件，增加: `"feconfigs.mcpServerProxyEndpoint": "fastgpt-mcp-server 的访问地址"`， 末尾不要携带/，例如:

```json
{
  "feConfigs": {
    "lafEnv": "https://laf.dev",
    "mcpServerProxyEndpoint": "https://mcp.fastgpt.cn"
  }
}
```

#### 商业版

在 Admin 后台，`系统配置-基础配置-系统参数`中的`MCP 转发服务地址`中，设置`fastgpt-mcp-server`的公网访问地址。

### 4. 更新镜像 tag

- 更新 FastGPT 镜像 tag: v4.9.6
- 更新 FastGPT 商业版镜像 tag: v4.9.6
- 更新 Sandbox 镜像 tag: v4.9.6
- 增加 FastGPT mcp server 镜像 tag: v4.9.6
- AIProxy 无需更新
