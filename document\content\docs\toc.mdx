---
title: FastGPT 文档目录
description: FastGPT 文档目录
---

- [/docs/faq/app](/docs/faq/app)
- [/docs/faq/chat](/docs/faq/chat)
- [/docs/faq/dataset](/docs/faq/dataset)
- [/docs/faq/error](/docs/faq/error)
- [/docs/faq/external_channel_integration](/docs/faq/external_channel_integration)
- [/docs/faq/other](/docs/faq/other)
- [/docs/faq/points_consumption](/docs/faq/points_consumption)
- [/docs/introduction/cloud](/docs/introduction/cloud)
- [/docs/introduction/commercial](/docs/introduction/commercial)
- [/docs/introduction/development/community](/docs/introduction/development/community)
- [/docs/introduction/development/configuration](/docs/introduction/development/configuration)
- [/docs/introduction/development/custom-models/bge-rerank](/docs/introduction/development/custom-models/bge-rerank)
- [/docs/introduction/development/custom-models/chatglm2](/docs/introduction/development/custom-models/chatglm2)
- [/docs/introduction/development/custom-models/chatglm2-m3e](/docs/introduction/development/custom-models/chatglm2-m3e)
- [/docs/introduction/development/custom-models/m3e](/docs/introduction/development/custom-models/m3e)
- [/docs/introduction/development/custom-models/marker](/docs/introduction/development/custom-models/marker)
- [/docs/introduction/development/custom-models/ollama](/docs/introduction/development/custom-models/ollama)
- [/docs/introduction/development/custom-models/xinference](/docs/introduction/development/custom-models/xinference)
- [/docs/introduction/development/design/dataset](/docs/introduction/development/design/dataset)
- [/docs/introduction/development/design/design_plugin](/docs/introduction/development/design/design_plugin)
- [/docs/introduction/development/docker](/docs/introduction/development/docker)
- [/docs/introduction/development/faq](/docs/introduction/development/faq)
- [/docs/introduction/development/intro](/docs/introduction/development/intro)
- [/docs/introduction/development/migration/docker_db](/docs/introduction/development/migration/docker_db)
- [/docs/introduction/development/migration/docker_mongo](/docs/introduction/development/migration/docker_mongo)
- [/docs/introduction/development/modelConfig/ai-proxy](/docs/introduction/development/modelConfig/ai-proxy)
- [/docs/introduction/development/modelConfig/intro](/docs/introduction/development/modelConfig/intro)
- [/docs/introduction/development/modelConfig/one-api](/docs/introduction/development/modelConfig/one-api)
- [/docs/introduction/development/modelConfig/ppio](/docs/introduction/development/modelConfig/ppio)
- [/docs/introduction/development/modelConfig/siliconCloud](/docs/introduction/development/modelConfig/siliconCloud)
- [/docs/introduction/development/openapi/chat](/docs/introduction/development/openapi/chat)
- [/docs/introduction/development/openapi/dataset](/docs/introduction/development/openapi/dataset)
- [/docs/introduction/development/openapi/intro](/docs/introduction/development/openapi/intro)
- [/docs/introduction/development/openapi/share](/docs/introduction/development/openapi/share)
- [/docs/introduction/development/proxy/cloudflare](/docs/introduction/development/proxy/cloudflare)
- [/docs/introduction/development/proxy/http_proxy](/docs/introduction/development/proxy/http_proxy)
- [/docs/introduction/development/proxy/nginx](/docs/introduction/development/proxy/nginx)
- [/docs/introduction/development/sealos](/docs/introduction/development/sealos)
- [/docs/introduction/guide/DialogBoxes/htmlRendering](/docs/introduction/guide/DialogBoxes/htmlRendering)
- [/docs/introduction/guide/DialogBoxes/quoteList](/docs/introduction/guide/DialogBoxes/quoteList)
- [/docs/introduction/guide/admin/sso](/docs/introduction/guide/admin/sso)
- [/docs/introduction/guide/admin/teamMode](/docs/introduction/guide/admin/teamMode)
- [/docs/introduction/guide/course/ai_settings](/docs/introduction/guide/course/ai_settings)
- [/docs/introduction/guide/course/chat_input_guide](/docs/introduction/guide/course/chat_input_guide)
- [/docs/introduction/guide/course/fileInput](/docs/introduction/guide/course/fileInput)
- [/docs/introduction/guide/course/quick-start](/docs/introduction/guide/course/quick-start)
- [/docs/introduction/guide/dashboard/basic-mode](/docs/introduction/guide/dashboard/basic-mode)
- [/docs/introduction/guide/dashboard/evaluation](/docs/introduction/guide/dashboard/evaluation)
- [/docs/introduction/guide/dashboard/gapier](/docs/introduction/guide/dashboard/gapier)
- [/docs/introduction/guide/dashboard/intro](/docs/introduction/guide/dashboard/intro)
- [/docs/introduction/guide/dashboard/mcp_server](/docs/introduction/guide/dashboard/mcp_server)
- [/docs/introduction/guide/dashboard/mcp_tools](/docs/introduction/guide/dashboard/mcp_tools)
- [/docs/introduction/guide/dashboard/workflow/ai_chat](/docs/introduction/guide/dashboard/workflow/ai_chat)
- [/docs/introduction/guide/dashboard/workflow/content_extract](/docs/introduction/guide/dashboard/workflow/content_extract)
- [/docs/introduction/guide/dashboard/workflow/coreferenceResolution](/docs/introduction/guide/dashboard/workflow/coreferenceResolution)
- [/docs/introduction/guide/dashboard/workflow/custom_feedback](/docs/introduction/guide/dashboard/workflow/custom_feedback)
- [/docs/introduction/guide/dashboard/workflow/dataset_search](/docs/introduction/guide/dashboard/workflow/dataset_search)
- [/docs/introduction/guide/dashboard/workflow/document_parsing](/docs/introduction/guide/dashboard/workflow/document_parsing)
- [/docs/introduction/guide/dashboard/workflow/form_input](/docs/introduction/guide/dashboard/workflow/form_input)
- [/docs/introduction/guide/dashboard/workflow/http](/docs/introduction/guide/dashboard/workflow/http)
- [/docs/introduction/guide/dashboard/workflow/knowledge_base_search_merge](/docs/introduction/guide/dashboard/workflow/knowledge_base_search_merge)
- [/docs/introduction/guide/dashboard/workflow/laf](/docs/introduction/guide/dashboard/workflow/laf)
- [/docs/introduction/guide/dashboard/workflow/loop](/docs/introduction/guide/dashboard/workflow/loop)
- [/docs/introduction/guide/dashboard/workflow/question_classify](/docs/introduction/guide/dashboard/workflow/question_classify)
- [/docs/introduction/guide/dashboard/workflow/reply](/docs/introduction/guide/dashboard/workflow/reply)
- [/docs/introduction/guide/dashboard/workflow/sandbox](/docs/introduction/guide/dashboard/workflow/sandbox)
- [/docs/introduction/guide/dashboard/workflow/text_editor](/docs/introduction/guide/dashboard/workflow/text_editor)
- [/docs/introduction/guide/dashboard/workflow/tfswitch](/docs/introduction/guide/dashboard/workflow/tfswitch)
- [/docs/introduction/guide/dashboard/workflow/tool](/docs/introduction/guide/dashboard/workflow/tool)
- [/docs/introduction/guide/dashboard/workflow/user-selection](/docs/introduction/guide/dashboard/workflow/user-selection)
- [/docs/introduction/guide/dashboard/workflow/variable_update](/docs/introduction/guide/dashboard/workflow/variable_update)
- [/docs/introduction/guide/knowledge_base/RAG](/docs/introduction/guide/knowledge_base/RAG)
- [/docs/introduction/guide/knowledge_base/api_dataset](/docs/introduction/guide/knowledge_base/api_dataset)
- [/docs/introduction/guide/knowledge_base/collection_tags](/docs/introduction/guide/knowledge_base/collection_tags)
- [/docs/introduction/guide/knowledge_base/dataset_engine](/docs/introduction/guide/knowledge_base/dataset_engine)
- [/docs/introduction/guide/knowledge_base/lark_dataset](/docs/introduction/guide/knowledge_base/lark_dataset)
- [/docs/introduction/guide/knowledge_base/template](/docs/introduction/guide/knowledge_base/template)
- [/docs/introduction/guide/knowledge_base/third_dataset](/docs/introduction/guide/knowledge_base/third_dataset)
- [/docs/introduction/guide/knowledge_base/websync](/docs/introduction/guide/knowledge_base/websync)
- [/docs/introduction/guide/knowledge_base/yuque_dataset](/docs/introduction/guide/knowledge_base/yuque_dataset)
- [/docs/introduction/guide/plugins/bing_search_plugin](/docs/introduction/guide/plugins/bing_search_plugin)
- [/docs/introduction/guide/plugins/dev_system_tool](/docs/introduction/guide/plugins/dev_system_tool)
- [/docs/introduction/guide/plugins/doc2x_plugin_guide](/docs/introduction/guide/plugins/doc2x_plugin_guide)
- [/docs/introduction/guide/plugins/google_search_plugin_guide](/docs/introduction/guide/plugins/google_search_plugin_guide)
- [/docs/introduction/guide/plugins/searxng_plugin_guide](/docs/introduction/guide/plugins/searxng_plugin_guide)
- [/docs/introduction/guide/team_permissions/invitation_link](/docs/introduction/guide/team_permissions/invitation_link)
- [/docs/introduction/guide/team_permissions/team_roles_permissions](/docs/introduction/guide/team_permissions/team_roles_permissions)
- [/docs/introduction/index](/docs/introduction/index)
- [/docs/protocol/open-source](/docs/protocol/open-source)
- [/docs/protocol/privacy](/docs/protocol/privacy)
- [/docs/protocol/terms](/docs/protocol/terms)
- [/docs/upgrading/4-10/4100](/docs/upgrading/4-10/4100)
- [/docs/upgrading/4-10/4101](/docs/upgrading/4-10/4101)
- [/docs/upgrading/4-11/4110](/docs/upgrading/4-11/4110)
- [/docs/upgrading/4-11/4111](/docs/upgrading/4-11/4111)
- [/docs/upgrading/4-12/4120](/docs/upgrading/4-12/4120)
- [/docs/upgrading/4-12/4121](/docs/upgrading/4-12/4121)
- [/docs/upgrading/4-8/40](/docs/upgrading/4-8/40)
- [/docs/upgrading/4-8/41](/docs/upgrading/4-8/41)
- [/docs/upgrading/4-8/42](/docs/upgrading/4-8/42)
- [/docs/upgrading/4-8/421](/docs/upgrading/4-8/421)
- [/docs/upgrading/4-8/43](/docs/upgrading/4-8/43)
- [/docs/upgrading/4-8/44](/docs/upgrading/4-8/44)
- [/docs/upgrading/4-8/441](/docs/upgrading/4-8/441)
- [/docs/upgrading/4-8/442](/docs/upgrading/4-8/442)
- [/docs/upgrading/4-8/445](/docs/upgrading/4-8/445)
- [/docs/upgrading/4-8/446](/docs/upgrading/4-8/446)
- [/docs/upgrading/4-8/447](/docs/upgrading/4-8/447)
- [/docs/upgrading/4-8/45](/docs/upgrading/4-8/45)
- [/docs/upgrading/4-8/451](/docs/upgrading/4-8/451)
- [/docs/upgrading/4-8/452](/docs/upgrading/4-8/452)
- [/docs/upgrading/4-8/46](/docs/upgrading/4-8/46)
- [/docs/upgrading/4-8/461](/docs/upgrading/4-8/461)
- [/docs/upgrading/4-8/462](/docs/upgrading/4-8/462)
- [/docs/upgrading/4-8/463](/docs/upgrading/4-8/463)
- [/docs/upgrading/4-8/464](/docs/upgrading/4-8/464)
- [/docs/upgrading/4-8/465](/docs/upgrading/4-8/465)
- [/docs/upgrading/4-8/466](/docs/upgrading/4-8/466)
- [/docs/upgrading/4-8/467](/docs/upgrading/4-8/467)
- [/docs/upgrading/4-8/468](/docs/upgrading/4-8/468)
- [/docs/upgrading/4-8/469](/docs/upgrading/4-8/469)
- [/docs/upgrading/4-8/47](/docs/upgrading/4-8/47)
- [/docs/upgrading/4-8/471](/docs/upgrading/4-8/471)
- [/docs/upgrading/4-8/48](/docs/upgrading/4-8/48)
- [/docs/upgrading/4-8/481](/docs/upgrading/4-8/481)
- [/docs/upgrading/4-8/4810](/docs/upgrading/4-8/4810)
- [/docs/upgrading/4-8/4811](/docs/upgrading/4-8/4811)
- [/docs/upgrading/4-8/4812](/docs/upgrading/4-8/4812)
- [/docs/upgrading/4-8/4813](/docs/upgrading/4-8/4813)
- [/docs/upgrading/4-8/4814](/docs/upgrading/4-8/4814)
- [/docs/upgrading/4-8/4815](/docs/upgrading/4-8/4815)
- [/docs/upgrading/4-8/4816](/docs/upgrading/4-8/4816)
- [/docs/upgrading/4-8/4817](/docs/upgrading/4-8/4817)
- [/docs/upgrading/4-8/4818](/docs/upgrading/4-8/4818)
- [/docs/upgrading/4-8/4819](/docs/upgrading/4-8/4819)
- [/docs/upgrading/4-8/482](/docs/upgrading/4-8/482)
- [/docs/upgrading/4-8/4820](/docs/upgrading/4-8/4820)
- [/docs/upgrading/4-8/4821](/docs/upgrading/4-8/4821)
- [/docs/upgrading/4-8/4822](/docs/upgrading/4-8/4822)
- [/docs/upgrading/4-8/4823](/docs/upgrading/4-8/4823)
- [/docs/upgrading/4-8/483](/docs/upgrading/4-8/483)
- [/docs/upgrading/4-8/484](/docs/upgrading/4-8/484)
- [/docs/upgrading/4-8/485](/docs/upgrading/4-8/485)
- [/docs/upgrading/4-8/486](/docs/upgrading/4-8/486)
- [/docs/upgrading/4-8/487](/docs/upgrading/4-8/487)
- [/docs/upgrading/4-8/488](/docs/upgrading/4-8/488)
- [/docs/upgrading/4-8/489](/docs/upgrading/4-8/489)
- [/docs/upgrading/4-9/490](/docs/upgrading/4-9/490)
- [/docs/upgrading/4-9/491](/docs/upgrading/4-9/491)
- [/docs/upgrading/4-9/4910](/docs/upgrading/4-9/4910)
- [/docs/upgrading/4-9/4911](/docs/upgrading/4-9/4911)
- [/docs/upgrading/4-9/4912](/docs/upgrading/4-9/4912)
- [/docs/upgrading/4-9/4913](/docs/upgrading/4-9/4913)
- [/docs/upgrading/4-9/4914](/docs/upgrading/4-9/4914)
- [/docs/upgrading/4-9/492](/docs/upgrading/4-9/492)
- [/docs/upgrading/4-9/493](/docs/upgrading/4-9/493)
- [/docs/upgrading/4-9/494](/docs/upgrading/4-9/494)
- [/docs/upgrading/4-9/495](/docs/upgrading/4-9/495)
- [/docs/upgrading/4-9/496](/docs/upgrading/4-9/496)
- [/docs/upgrading/4-9/497](/docs/upgrading/4-9/497)
- [/docs/upgrading/4-9/498](/docs/upgrading/4-9/498)
- [/docs/upgrading/4-9/499](/docs/upgrading/4-9/499)
- [/docs/use-cases/app-cases/dalle3](/docs/use-cases/app-cases/dalle3)
- [/docs/use-cases/app-cases/english_essay_correction_bot](/docs/use-cases/app-cases/english_essay_correction_bot)
- [/docs/use-cases/app-cases/feishu_webhook](/docs/use-cases/app-cases/feishu_webhook)
- [/docs/use-cases/app-cases/fixingEvidence](/docs/use-cases/app-cases/fixingEvidence)
- [/docs/use-cases/app-cases/google_search](/docs/use-cases/app-cases/google_search)
- [/docs/use-cases/app-cases/lab_appointment](/docs/use-cases/app-cases/lab_appointment)
- [/docs/use-cases/app-cases/multi_turn_translation_bot](/docs/use-cases/app-cases/multi_turn_translation_bot)
- [/docs/use-cases/app-cases/submit_application_template](/docs/use-cases/app-cases/submit_application_template)
- [/docs/use-cases/app-cases/translate-subtitle-using-gpt](/docs/use-cases/app-cases/translate-subtitle-using-gpt)
- [/docs/use-cases/external-integration/dingtalk](/docs/use-cases/external-integration/dingtalk)
- [/docs/use-cases/external-integration/feishu](/docs/use-cases/external-integration/feishu)
- [/docs/use-cases/external-integration/official_account](/docs/use-cases/external-integration/official_account)
- [/docs/use-cases/external-integration/openapi](/docs/use-cases/external-integration/openapi)
