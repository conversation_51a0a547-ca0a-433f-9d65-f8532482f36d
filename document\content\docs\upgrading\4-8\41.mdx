---
title: V4.1
description: FastGPT 从旧版本升级到 V4.1 操作指南
---

如果您是**从旧版本升级到 V4.1**，由于新版重新设置了对话存储结构，需要初始化原来的存储内容。

## 更新环境变量

V4.1 优化了 PostgreSQL 和 MongoDB 的连接变量，只需要填 1 个 URL 即可：

注意：/fastgpt 和 /postgres 是指数据库名称，需要和旧版的变量对应。

```bash
# mongo 配置，不需要改. 如果连不上，可能需要去掉 ?authSource=admin
- MONGODB_URI=****************************************************************
# pg配置. 不需要改
- PG_URL=**************************************/postgres
```

## 初始化 API

部署新版项目，并发起 1 个 HTTP 请求（记得携带 `headers.rootkey`，这个值是环境变量里的）

- https://xxxxx/api/admin/initChatItem
