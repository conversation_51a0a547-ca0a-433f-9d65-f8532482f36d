---
title: 飞书知识库
description: FastGPT 飞书知识库功能介绍和使用方式
---

| | |
| --- | --- |
| ![alt text](/imgs/image-39.png) | ![alt text](/imgs/image-40.png) |

FastGPT v4.8.16 版本开始，商业版用户支持飞书知识库导入，用户可以通过配置飞书应用的 appId 和 appSecret，并选中一个**文档空间的顶层文件夹**来导入飞书知识库。目前处于测试阶段，部分交互有待优化。

由于飞书限制，无法直接获取所有文档内容，目前仅可以获取共享空间下文件目录的内容，无法获取个人空间和知识库里的内容。


## 1. 创建飞书应用

打开 [飞书开放平台](https://open.feishu.cn/?lang=zh-CN)，点击**创建应用**，选择**自建应用**，然后填写应用名称。

## 2. 配置应用权限

创建应用后，进入应用可以配置相关权限，这里需要增加**3个权限**：

1. 获取云空间文件夹下的云文档清单
2. 查看新版文档
3. 查看、评论、编辑和管理云空间中所有文件

![alt text](/imgs/image-41.png)

## 3. 获取 appId 和 appSecret

![alt text](/imgs/image-42.png)

## 4. 给 Folder 增加权限

可参考飞书教程： https://open.feishu.cn/document/server-docs/docs/drive-v1/faq#b02e5bfb

大致总结为：

1. 把刚刚创建的应用拉入一个群里
2. 给这个群增加目录权限

如果你的目录已经给全员组增加权限了，则可以跳过上面步骤，直接获取 Folder Token。

![alt text](/imgs/image-43.png)

## 5. 获取 Folder Token

可以页面路径上获取 Folder Token，注意不要把问号复制进来。

![alt text](/imgs/image-44.png)

## 6. 创建知识库

根据 3 和 5 获取到的 3 个参数，创建知识库，选择飞书文件库类型，然后填入对应的参数，点击创建。

![alt text](/imgs/image-39.png)
