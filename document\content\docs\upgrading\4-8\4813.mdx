---
title: V4.8.13
description: FastGPT V4.8.13 更新说明
---

## 更新指南

### 1. 做好数据备份

### 2. 修改镜像

- 更新 FastGPT 镜像 tag: v4.8.13-fix
- 更新 FastGPT 商业版镜像 tag: v4.8.13-fix （fastgpt-pro镜像）
- Sandbox 镜像，可以不更新

### 3. 添加环境变量

- 给 fastgpt 和 fastgpt-pro 镜像添加环境变量：`FE_DOMAIN=http://xx.com`，值为 fastgpt 前端访问地址，注意后面不要加`/`。可以自动补齐相对文件地址的前缀。

### 4. 调整文件上传编排

虽然依然兼容旧版的文件上传编排，但是未来两个版本内将会去除兼容代码，请尽快调整编排，以适应最新的文件上传逻辑。尤其是嵌套应用的文件传递，未来将不会自动传递，必须手动指定传递的文件。具体内容可参考: [文件上传变更](/docs/introduction/guide/course/fileinput/#4813%E7%89%88%E6%9C%AC%E8%B5%B7%E5%85%B3%E4%BA%8E%E6%96%87%E4%BB%B6%E4%B8%8A%E4%BC%A0%E7%9A%84%E6%9B%B4%E6%96%B0)

## 更新说明

1. 新增 - 数组变量选择支持多选，可以选多个数组或对应的单一数据类型，会自动按选择顺序进行合并。
2. 新增 - 文件上传方案调整，AI对话和工具调用节点直接支持接收文件链接，并且会强制加入提示词，无需由模型决策调用。插件自定义变量支持文件上传类型，取代全局文件。 
3. 新增 - 对话记录增加时间显示。 
4. 新增 - 工作流校验错误时，跳转至错误节点。  
5. 新增 - 循环节点增加下标值。 
6. 新增 - 部分对话错误提醒增加翻译。 
7. 新增 - 对话输入框支持拖拽文件上传，可直接拖文件到输入框中。  
8. 新增 - 对话日志，来源可显示分享链接/API具体名称
9. 新增 - 分享链接支持配置是否展示实时运行状态。
10. 优化 - 合并多个 system 提示词成 1 个，避免部分模型不支持多个 system 提示词。  
11. 优化 - 知识库上传文件，优化报错提示。  
12. 优化 - 全文检索语句，减少一轮子查询。  
13. 优化 - 修改 findLast 为 [...array].reverse().find，适配旧版浏览器。  
14. 优化 - Markdown 组件自动空格，避免分割 url 中的中文。  
15. 优化 - 工作流上下文拆分，性能优化。 
16. 优化 - 语音播报，不支持 mediaSource 的浏览器可等待完全生成语音后输出。 
17. 优化 - 对话引导 csv 读取，自动识别编码
18. 优化 - csv 导入问题引导可能乱码
19. 修复 - Dockerfile pnpm install 支持代理。。
20. 修复 - Dockerfile pnpm install 支持代理。 
21. 修复 - BI 图表生成无法写入文件。同时优化其解析，支持数字类型数组。
22. 修复 - 分享链接首次加载时，标题显示不正确。 
