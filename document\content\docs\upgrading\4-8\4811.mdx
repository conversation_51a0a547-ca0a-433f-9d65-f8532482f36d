---
title: V4.8.11（商业版初始化）
description: FastGPT V4.8.11 更新说明
---

## 更新指南

### 1. 做好数据备份

### 2. 修改配置文件

如需增加 openai o1 模型，可添加如下配置：

```json
{
    "model": "o1-mini",
    "name": "o1-mini",
    "avatar": "/imgs/model/openai.svg",
    "maxContext": 125000,
    "maxResponse": 65000,
    "quoteMaxToken": 120000,
    "maxTemperature": 1.2,
    "charsPointsPrice": 0,
    "censor": false,
    "vision": false,
    "datasetProcess": true,
    "usedInClassify": true,
    "usedInExtractFields": true,
    "usedInToolCall": true,
    "toolChoice": false,
    "functionCall": false,
    "customCQPrompt": "",
    "customExtractPrompt": "",
    "defaultSystemChatPrompt": "",
    "defaultConfig": {
        "temperature": 1
    }
},
{
    "model": "o1-preview",
    "name": "o1-preview",
    "avatar": "/imgs/model/openai.svg",
    "maxContext": 125000,
    "maxResponse": 32000,
    "quoteMaxToken": 120000,
    "maxTemperature": 1.2,
    "charsPointsPrice": 0,
    "censor": false,
    "vision": false,
    "datasetProcess": true,
    "usedInClassify": true,
    "usedInExtractFields": true,
    "usedInToolCall": true,
    "toolChoice": false,
    "functionCall": false,
    "customCQPrompt": "",
    "customExtractPrompt": "",
    "defaultSystemChatPrompt": "",
    "defaultConfig": {
        "temperature": 1
    }
}
```

-------

### 3. 修改镜像 tag 并重启

- 更新 FastGPT 镜像 tag: v4.8.11-fix
- 更新 FastGPT 商业版镜像 tag: v4.8.11
- 更新 FastGPT Sandbox 镜像 tag: v4.8.11

### 4. 商业版初始化

从任意终端，发起 1 个 HTTP 请求。其中 `{{rootkey}}` 替换成环境变量里的 `rootkey`；`{{host}}` 替换成**FastGPT 域名**:

```bash
curl --location --request POST 'https://{{host}}/api/admin/init/4811' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

会初始化团队成员组。

## V4.8.11 更新说明

1. 新增 - 表单输入节点，允许用户在工作流中让用户输入一些信息。
2. 新增 - 循环运行节点，可传入数组进行批量调用，目前最多支持 50 长度的数组串行执行。
3. 新增 - 节点支持折叠。
4. 新增 - 简易模式支持新的历史记录模式，可记录本地变更记录。
5. 新增 - 聊天记录滚动加载，不再只加载 30 条。
6. 新增 - 工作流增加触摸板优先模式，可以通过工作流右下角按键进行切换。
7. 新增 - 沙盒增加字符串转 base64 全局方法(全局变量 strToBase64)。
8. 新增 - 支持 Openai o1 模型，需增加模型的 `defaultConfig` 配置，覆盖 `temperature`、`max_tokens` 和 `stream`配置，o1 不支持 stream 模式。
9. 新增 - AI 对话节点知识库引用，支持配置 role=system 和 role=user，已配置的过自定义提示词的节点将会保持 user 模式，其余用户将转成 system 模式。
10. 新增 - 插件支持上传系统文件。
11. 新增 - 插件输出，支持指定字段作为工具响应。
12. 新增 - 支持工作流嵌套子应用时，可以设置`非流模式`，同时简易模式也可以选择工作流作为插件了，简易模式调用子应用时，都将强制使用非流模式。
13. 新增 - 调试模式下，子应用调用，支持返回详细运行数据。
14. 新增 - 保留所有模式下子应用嵌套调用的日志。
15. 新增 - 对话日志显示成员。
16. 新增 - 商业版支持后台配置 AI 生成文案提示。
17. 新增 - Jest 单测框架。
18. 新增 - 工具调用参数节点，可以配合工具调用完全自由声明参数。
19. 新增 - BI 图表插件。
20. 新增 - Surya OCR 识别模块示例。
21. 新增 - 工作流右键新增注释。
22. 商业版新增 - 团队成员组。
23. 优化 - 工作流嵌套层级限制 20 层，避免因编排不合理导致的无限死循环。
24. 优化 - 工作流 handler 性能优化。
25. 优化 - 工作流快捷键，避免调试测试时也会触发复制和回退。
26. 优化 - 工作流复制时，名字去掉多余“#”。
27. 优化 - 流输出，切换浏览器 Tab 后仍可以继续输出。
28. 优化 - 完善外部文件知识库相关 API 。
29. 优化 - 修改 config.json 的地址。
30. 优化 - 正确处理//开头的超链接。
31. 优化 - 工作流 Textarea 滚轮可以正常滚动而不是缩放。
32. 优化 - 去除部分输入框前后空格。
33. 优化 - 工作流返回时，跳转到上一次记忆的 Tab 。
34. 优化 - 工作流画布禁止触摸板缩放浏览器。
35. 优化 - 工作流部分节点会自动选择用户问题作为初始值。
36. 优化 - Prompt Editor 支持动态增高。
37. 优化 - 自动补全工具描述。
38. 优化 - 文档说明 configuration.md 。
39. 优化 - IOS safari 浏览器语音输入不准确。
40. 修复 - 知识库选择权限问题。
41. 修复 - 空 chatId 发起对话，首轮携带用户选择时会异常。
42. 修复 - createDataset 接口，intro 未赋值。
43. 修复 - 对话框渲染性能问题。
44. 修复 - Rerank 文档地址。
45. 修复 - Stream 模式下使用 toolChoice,toolCall 的function 和type 可能为 null 。
46. 修复 - 站点同步自定义分割符未同步。
47. 修复 - 工具调用历史记录存储问题。
48. 修复 - 对话页面可能无限重定向。
49. 修复 - 全局变量在工具调用中未持久传递。
