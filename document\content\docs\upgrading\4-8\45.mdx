---
title: V4.5(需进行较为复杂更新)
description: FastGPT V4.5 更新
---

FastGPT V4.5 引入 PgVector0.5 版本的 HNSW 索引，极大的提高了知识库检索的速度，比起`IVFFlat`索引大致有3~10倍的性能提升，可轻松实现百万数据毫秒级搜索。缺点在于构建索引的速度非常慢，4c16g 500w 组数据使用`并行构建`大约花了 48 小时。具体参数配置可参考 [PgVector官方](https://github.com/pgvector/pgvector)

下面需要对数据库进行一些操作升级：

## PgVector升级：Sealos 部署方案

1. 点击[Sealos桌面](https://cloud.sealos.io?uid=fnWRt09fZP)的数据库应用。
2. 点击【pg】数据库的详情。
3. 点击右上角的重启，等待重启完成。
4. 点击左侧的一键链接，等待打开 Terminal。
5. 依次输入下方 sql 命令

```sql
-- 升级插件名
ALTER EXTENSION vector UPDATE;
-- 插件是否升级成功，成功的话，vector插件版本为 0.5.0，旧版的为 0.4.1
\dx

-- 下面两个语句会设置 pg 在构建索引时可用的内存大小，需根据自身的数据库规格来动态配置，可配置为 1/4 的内存大小
alter system set maintenance_work_mem = '2400MB';
select pg_reload_conf();

-- 重构数据库索引和排序
REINDEX DATABASE postgres;

-- 开始构建索引，该索引构建时间非常久，直接点击右上角的叉，退出 Terminal 即可
CREATE INDEX CONCURRENTLY vector_index ON modeldata USING hnsw (vector vector_ip_ops) WITH (m = 16, ef_construction = 64);
-- 可以再次点击一键链接，进入 Terminal，输入下方命令，如果看到 "vector_index" hnsw (vector vector_ip_ops) WITH (m='16', ef_construction='64') 则代表构建完成（注意，后面没有 INVALID）
\d modeldata
```

|                      |                      |
| -------------------- | -------------------- |
| ![](/imgs/v45-1.jpg) | ![](/imgs/v45-2.jpg) |
| ![](/imgs/v45-3.jpg) | ![](/imgs/v45-4.jpg) |

## PgVector升级：Docker-compose.yml 部署方案

下面的命令是基于给的 docker-compose 模板，如果数据库账号密码更换了，请自行调整。

1. 修改 `docker-compose.yml` 中pg的镜像版本，改成 `ankane/pgvector:v0.5.0` 或 `registry.cn-hangzhou.aliyuncs.com/fastgpt/pgvector:v0.5.0`
2. 重启 pg 容器(docker-compose pull && docker-compose up -d)，等待重启完成。
3. 进入容器: `docker exec -it pg bash`
4. 连接数据库: `psql 'postgresql://username:password@localhost:5432/postgres'`
5. 执行下面 sql 命令

```sql
-- 升级插件名
ALTER EXTENSION vector UPDATE;
-- 插件是否升级成功，成功的话，vector插件版本为 0.5.0，旧版的为 0.4.2
\dx

-- 下面两个语句会设置 pg 在构建索引时可用的内存大小，需根据自身的数据库规格来动态配置，可配置为 1/4 的内存大小
alter system set maintenance_work_mem = '2400MB';
select pg_reload_conf();

-- 重构数据库索引和排序
REINDEX DATABASE postgres;
ALTER DATABASE postgres REFRESH COLLATION VERSION;

-- 开始构建索引，该索引构建时间非常久，直接关掉终端即可，不要使用 ctrl+c 关闭
CREATE INDEX CONCURRENTLY vector_index ON modeldata USING hnsw (vector vector_ip_ops) WITH (m = 16, ef_construction = 64);
-- 可以再次连接数据库，输入下方命令。如果看到 "vector_index" hnsw (vector vector_ip_ops) WITH (m='16', ef_construction='64') 则代表构建完成（注意，后面没有 INVALID）
\d modeldata


```

## 版本新功能介绍

### Fast GPT V4.5

1. 新增 - 升级 PgVector 插件，引入 HNSW 索引，极大加快的知识库搜索速度。
2. 新增 - AI对话模块，增加【返回AI内容】选项，可控制 AI 的内容不直接返回浏览器。
3. 新增 - 支持问题分类选择模型
4. 优化 - TextSplitter，采用递归拆解法。
5. 优化 - 高级编排 UX 性能
6. 修复 - 分享链接鉴权问题

## 该版本需要修改 `config.json` 文件

最新配置可参考: [V45版本最新 config.json](/docs/introduction/development/configuration)
