---
title: V4.8.8(包含升级脚本)
description: FastGPT V4.8.8 更新说明
---

## 升级指南

### 1. 做好数据库备份

### 2. 修改镜像

- fastgpt 镜像 tag 修改成 v4.8.8-fix2
- 商业版镜像 tag 修改成 v4.8.8

### 3. 执行初始化

从任意终端，发起 1 个 HTTP 请求。其中 `{{rootkey}}` 替换成环境变量里的 `rootkey`；`{{host}}` 替换成**FastGPT 域名**。

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv488' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

会初始化知识库的继承权限

-------

## V4.8.8 更新说明

[点击查看完整更新](https://github.com/labring/FastGPT/releases/tag/v4.8.8)

1. 新增 - 重构系统插件的结构。允许向开源社区 PR 系统插件，具体可见: [如何向 FastGPT 社区提交系统插件](https://fael3z0zfze.feishu.cn/wiki/ERZnw9R26iRRG0kXZRec6WL9nwh)。
2. 新增 - DuckDuckGo 系统插件。
3. 新增 - 飞书 webhook 系统插件。
4. 新增 - 修改变量填写方式。提示词输入框以以及工作流中所有 Textarea 输入框，支持输入 / 唤起变量选择，可直接选择所有上游输出值，无需动态引入。
5. 商业版新增 - 知识库权限继承。
6. 优化 - 移动端快速切换应用交互。
7. 优化 - 节点图标。
8. 优化 - 对话框引用增加额外复制案件，便于复制。增加引用内容折叠。
9. 优化 - OpenAI sdk 升级，并自定义了 whisper 模型接口（未仔细查看 sdk 实现，但 sdk 中 whisper 接口，似乎无法适配一般 fastapi 接口）
10. 修复 - Permission 表声明问题。
11. 修复 - 并行执行节点，运行时间未正确记录。
12. 修复 - 运行详情未正确展示嵌套节点信息。
13. 修复 - 简易模式，首次进入，无法正确获取知识库配置。
14. 修复 - Log debug level 配置无效。
15. 修复 - 插件独立运行时，会将插件输入的值进行变量替换，可能导致后续节点变量异常。
