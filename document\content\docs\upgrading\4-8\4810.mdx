---
title: V4.8.10(包含升级脚本)
description: FastGPT V4.8.10 更新说明
---

## 更新指南

### 1. 做好数据备份


### 2. 商业版 —— 修改环境变量

1. 需要给`fastgpt-pro`镜像，增加沙盒的环境变量：`SANDBOX_URL=http://xxxxx:3000`
2. 给`fastgpt-pro`镜像和`fastgpt`镜像增加环境变量，以便更好的存储系统日志：

```
LOG_LEVEL=debug
STORE_LOG_LEVEL=warn
```

### 3. 修改镜像tag

- 更新 FastGPT 镜像 tag: v4.8.10
- 更新 FastGPT 商业版镜像 tag: v4.8.10
- Sandbox 镜像，可以不更新

## 4. 执行初始化

从任意终端，发起 1 个 HTTP 请求。其中 `{{rootkey}}` 替换成环境变量里的 `rootkey`；`{{host}}` 替换成**FastGPT 域名**。

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv4810' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

1. 初始化发布记录版本标记
2. 初始化开票记录

-------

## V4.8.10 更新说明

完整内容请见：[4.8.10 release](https://github.com/labring/FastGPT/releases/tag/v4.8.10)

1. 新增 - 模板市场。
2. 新增 - 工作流节点拖动自动对齐吸附。
3. 新增 - 用户选择节点（Debug 模式暂未支持）。
4. 新增 - 工作流增加 uid 全局变量。
5. 新增 - 工作流撤销和重做。
6. 新增 - 工作流本次编辑记录，取代自动保存。
7. 新增 - 工作流版本支持重命名。
8. 新增 - 工作流的“应用调用”节点弃用，迁移成单独节点，与插件使用方式相同，同时可以传递全局变量和用户上传的文件。
9. 新增 - 插件增加使用说明配置。
10. 新增 - 插件自定义输入支持单选框。
11. 新增 - HTTP 节点支持 text/plain 模式。
12. 新增 - HTTP模块支持超时配置、支持更多的 Body 类型，params 和 headers 支持新的变量选择模式。
13. 新增 - 工作流导出导入，支持直接导出和导入 JSON 文件，便于交流。
14. 新增 - 发送验证码安全校验。
15. 商业版新增 - 飞书机器人接入。
16. 商业版新增 - 公众号接入接入。
17. 商业版新增 - 自助开票申请。
18. 商业版新增 - SSO 定制。
19. 优化 - 工作流循环校验，避免 skip 循环空转。同时支持分支完全并发执行。
20. 优化 - 工作流嵌套执行，参数可能存在的污染问题。
21. 优化 - 部分全局变量，增加数据类型约束。
22. 优化 - 节点选择，避免切换 tab 时候，path 加载报错。
23. 优化 - 最新 React Markdown 组件，支持 Base64 图片。
24. 优化 - 对话框性能问题。
25. 优化 - 单选框打开后自动滚动到选中的位置。
26. 优化 - 知识库集合禁用，目录禁用会递归修改其下所有 children 的禁用状态。
27. 优化 - SSE 响应代码优化。
28. 优化 - 无 SSL 证书情况下，优化复制。
29. 优化 - 知识库列表 UI。
30. 优化 - 知识库详情页 UI。
31. 优化 - 支持无网络配置情况下运行。
32. 优化 - 调整.env.template关于mongodb的说明，使得更易于理解。
33. 优化 - 新的支付模式。
34. 优化 - 用户默认头像。
35. 修复 - Prompt 模式调用工具，stream=false 模式下，会携带 0: 开头标记。
36. 修复 - 对话日志鉴权问题：仅为 APP 管理员的用户，无法查看对话日志详情。
37. 修复 - 选择 Milvus 部署时，无法导出知识库。
38. 修复 - 创建 APP 副本，无法复制系统配置。
39. 修复 - 图片识别模式下，自动解析图片链接正则不够严谨问题。
40. 修复 - 内容提取的数据类型与输出数据类型未一致。
41. 修复 - 工作流运行时间统计错误。
42. 修复 - stream 模式下，工具调用有可能出现 undefined。
43. 修复 - reranker typo。
44. 修复 - home host typo。
45. 修复 - i18n display。
46. 修复 - 全局变量可重复定义 key。
47. 修复 - 全局变量在 Debug 模式下不可持久化。
48. 修复 - 全局变量在 API 中无法持久化。
49. 修复 - OpenAPI，detail=false模式下，不应该返回 tool 调用结果，仅返回文字。（可解决 cow 不适配问题）。
50. 修复 - 知识库标签重复加载。
51. 修复 - 网络链接重新获取时，自定义分割符不生效。
52. 修复 - 插件运行时，会传递额外的全局变量，可能造成插件内变量污染。
53. 文档 - qa docs。
54. 文档 - Update feishu.md。
55. 文档 - update baseURL。
