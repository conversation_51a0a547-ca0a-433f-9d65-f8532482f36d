---
title: V4.9.0(包含升级脚本)
description: FastGPT V4.9.0 更新说明
---

## 更新指南

### 1. 做好数据库备份

### 2. 更新镜像和 PG 容器

- 更新 FastGPT 镜像 tag: v4.9.0
- 更新 FastGPT 商业版镜像 tag: v4.9.0
- Sandbox 镜像，可以不更新
- 更新 PG 容器为 v0.8.0-pg15, 可以查看[最新的 yml](https://raw.githubusercontent.com/labring/FastGPT/main/deploy/docker/docker-compose-pgvector.yml)

### 3. 替换 OneAPI（可选）

如果需要使用 [AI Proxy](https://github.com/labring/aiproxy) 替换 OneAPI 的用户可执行该步骤。

#### 1. 修改 yml 文件

参考[最新的 yml](https://raw.githubusercontent.com/labring/FastGPT/main/deploy/docker/docker-compose-pgvector.yml) 文件。里面已移除 OneAPI 并添加了 AIProxy配置。包含一个服务和一个 PgSQL 数据库。将 `aiproxy` 的配置`追加`到 OneAPI 的配置后面（先不要删除 OneAPI，有一个初始化会自动同步 OneAPI 的配置）

<details>
<summary>AI Proxy Yml 配置</summary>

```
  # AI Proxy
  aiproxy:
    image: 'ghcr.io/labring/aiproxy:latest'
    container_name: aiproxy
    restart: unless-stopped
    depends_on:
      aiproxy_pg:
        condition: service_healthy
    networks:
      - fastgpt
    environment:
      # 对应 fastgpt 里的AIPROXY_API_TOKEN
      - ADMIN_KEY=aiproxy
      # 错误日志详情保存时间（小时）
      - LOG_DETAIL_STORAGE_HOURS=1
      # 数据库连接地址
      - SQL_DSN=*******************************************/aiproxy
      # 最大重试次数
      - RETRY_TIMES=3
      # 不需要计费
      - BILLING_ENABLED=false
      # 不需要严格检测模型
      - DISABLE_MODEL_CONFIG=true
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/api/status']
      interval: 5s
      timeout: 5s
      retries: 10
  aiproxy_pg:
    image: pgvector/pgvector:0.8.0-pg15 # docker hub
    # image: registry.cn-hangzhou.aliyuncs.com/fastgpt/pgvector:v0.8.0-pg15 # 阿里云
    restart: unless-stopped
    container_name: aiproxy_pg
    volumes:
      - ./aiproxy_pg:/var/lib/postgresql/data
    networks:
      - fastgpt
    environment:
      TZ: Asia/Shanghai
      POSTGRES_USER: postgres
      POSTGRES_DB: aiproxy
      POSTGRES_PASSWORD: aiproxy
    healthcheck:
      test: ['CMD', 'pg_isready', '-U', 'postgres', '-d', 'aiproxy']
      interval: 5s
      timeout: 5s
      retries: 10
```

</details>

#### 2. 增加 FastGPT 环境变量：

修改 yml 文件中，fastgpt 容器的环境变量：

```
# AI Proxy 的地址，如果配了该地址，优先使用
- AIPROXY_API_ENDPOINT=http://aiproxy:3000
# AI Proxy 的 Admin Token，与 AI Proxy 中的环境变量 ADMIN_KEY
- AIPROXY_API_TOKEN=aiproxy
```

#### 3. 重载服务

`docker-compose down` 停止服务，然后 `docker-compose up -d` 启动服务，此时会追加 `aiproxy` 服务，并修改 FastGPT 的配置。

#### 4. 执行OneAPI迁移AI proxy脚本

- 可联网方案:

```bash
# 进入 aiproxy 容器
docker exec -it aiproxy sh
# 安装 curl
apk add curl
# 执行脚本
curl --location --request POST 'http://localhost:3000/api/channels/import/oneapi' \
--header 'Authorization: Bearer aiproxy' \
--header 'Content-Type: application/json' \
--data-raw '{
    "dsn": "mysql://root:oneapimmysql@tcp(mysql:3306)/oneapi"
}'
# 返回 {"data":[],"success":true} 代表成功
```

- 无法联网时，可打开`aiproxy`的外网暴露端口，然后在本地执行脚本。

aiProxy 暴露端口：3003:3000，修改后重新 `docker-compose up -d` 启动服务。

```bash
# 在终端执行脚本
curl --location --request POST 'http://localhost:3003/api/channels/import/oneapi' \
--header 'Authorization: Bearer aiproxy' \
--header 'Content-Type: application/json' \
--data-raw '{
    "dsn": "mysql://root:oneapimmysql@tcp(mysql:3306)/oneapi"
}'
# 返回 {"data":[],"success":true} 代表成功
```

- 如果不熟悉 docker 操作，建议不要走脚本迁移，直接删除 OneAPI 所有内容，然后手动重新添加渠道。

#### 5. 进入 FastGPT 检查`AI Proxy` 服务是否正常启动。

登录 root 账号后，在`账号-模型提供商`页面，可以看到多出了`模型渠道`和`调用日志`两个选项，打开模型渠道，可以看到之前 OneAPI 的渠道，说明迁移完成，此时可以手动再检查下渠道是否正常。

#### 6. 删除 OneAPI 服务

```bash
# 停止服务，或者针对性停止 OneAPI 和其 Mysql
docker-compose down
# yml 文件中删除 OneAPI 和其 Mysql 依赖
# 重启服务
docker-compose up -d
```

### 4. 运行 FastGPT 升级脚本

从任意终端，发起 1 个 HTTP 请求。其中 `{{rootkey}}` 替换成环境变量里的 `rootkey`；`{{host}}` 替换成**FastGPT 域名**。

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv490' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

**脚本功能**

1. 升级 PG Vector 插件版本
2. 全量更新知识库集合字段。
3. 全量更新知识库数据中，index 的 type 类型。（时间较长，最后可能提示 timeout，可忽略，数据库不崩都会一直增量执行）

## 兼容 & 弃用

1. 弃用 - 之前私有化部署的自定义文件解析方案，请同步更新到最新的配置方案。[点击查看 PDF 增强解析配置](/docs/introduction/development/configuration/#使用-doc2x-解析-pdf-文件)
2. 弃用 - 弃用旧版本地文件上传 API：/api/core/dataset/collection/create/file（以前仅商业版可用的 API，该接口已放切换成：/api/core/dataset/collection/create/localFile）
3. 停止维护，即将弃用 - 外部文件库相关 API，可通过 API 文件库替代。
4. API更新 - 上传文件至知识库、创建连接集合、API 文件库、推送分块数据等带有 `trainingType` 字段的接口，`trainingType`字段未来仅支持`chunk`和`QA`两种模式。增强索引模式将设置单独字段：`autoIndexes`，目前仍有适配旧版`trainingType=auto`代码，但请尽快变更成新接口类型。具体可见：[知识库 OpenAPI 文档](/docs/introduction/development/openapi/dataset.md)

## 🚀 新增内容

1. PDF增强解析交互添加到页面上。同时内嵌 Doc2x 服务，可直接使用 Doc2x 服务解析 PDF 文件。
2. 图片自动标注，同时修改知识库文件上传部分数据逻辑和交互。
3. pg vector 插件升级 0.8.0 版本，引入迭代搜索，减少部分数据无法被检索的情况。
4. 新增 qwen-qwq 系列模型配置。

## ⚙️ 优化

1. 知识库数据不再限制索引数量，可无限自定义。同时可自动更新输入文本的索引，不影响自定义索引。
2. Markdown 解析，增加链接后中文标点符号检测，增加空格。
3. Prompt 模式工具调用，支持思考模型。同时优化其格式检测，减少空输出的概率。
4. Mongo 文件读取流合并，减少计算量。同时优化存储 chunks，极大提高大文件读取速度。50M PDF 读取时间提高 3 倍。
5. HTTP Body 适配，增加对字符串对象的适配。

## 🐛 修复

1. 增加网页抓取安全链接校验。
2. 批量运行时，全局变量未进一步传递到下一次运行中，导致最终变量更新错误。
