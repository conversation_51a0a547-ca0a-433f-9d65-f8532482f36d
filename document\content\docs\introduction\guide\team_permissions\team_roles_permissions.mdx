---
title: 团队&成员组&权限
description: 如何管理 FastGPT 团队、成员组及权限设置
---

# 团队 & 成员组 & 权限

## 权限系统简介

FastGPT
权限系统融合了基于**属性**和基于**角色**的权限管理范式，为团队协作提供精细化的权限控制方案。通过**成员、部门和群组**三种管理模式，您可以灵活配置对团队、应用和知识库等资源的访问权限。

## 团队

每位用户可以同时归属于多个团队，系统默认为每位用户创建一个初始团队。目前暂不支持用户手动创建额外团队。

## 权限管理

FastGPT 提供三种权限管理维度：

**成员权限**：最高优先级，直接赋予个人的权限

**部门与群组权限**：采用权限并集原则，优先级低于成员权限

权限判定遵循以下逻辑：

首先检查用户的个人成员权限

其次检查用户所属部门和群组的权限（取并集）

最终权限为上述结果的组合

鉴权逻辑如下：

![](/imgs/guide/team_permissions/team_roles_permissions/image1.jpeg)

### 资源权限

对于不同的**资源**，有不同的权限。

这里说的资源，是指应用、知识库、团队等等概念。

下表为不同资源，可以进行管理的权限。

  <table>
    <thead>
      <tr>
        <th>资源</th>
        <th>可管理权限</th>
        <th>说明</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td rowSpan="4">团队</td>
        <td>创建应用</td>
        <td>创建，删除等基础操作</td>
      </tr>
      <tr>
        <td>创建知识库</td>
        <td>创建，删除等基础操作</td>
      </tr>
      <tr>
        <td>创建团队 APIKey</td>
        <td>创建，删除等基础操作</td>
      </tr>
      <tr>
        <td>管理成员</td>
        <td>邀请、移除用户，创建群组等</td>
      </tr>
      <tr>
        <td rowSpan="3">应用</td>
        <td>可使用</td>
        <td>允许进行对话交互</td>
      </tr>
      <tr>
        <td>可编辑</td>
        <td>修改基本信息，进行流程编排等</td>
      </tr>
      <tr>
        <td>可管理</td>
        <td>添加或删除协作者</td>
      </tr>
      <tr>
        <td rowSpan="3">知识库</td>
        <td>可使用</td>
        <td>可以在应用中调用该知识库</td>
      </tr>
      <tr>
        <td>可编辑</td>
        <td>修改知识库的内容</td>
      </tr>
      <tr>
        <td>可管理</td>
        <td>添加或删除协作者</td>
      </tr>
    </tbody>
  </table>

### 协作者

必须先添加**协作者**，才能对其进行权限管理：

![](/imgs/guide/team_permissions/team_roles_permissions/image2.png)

管理团队权限时，需先选择成员/组织/群组，再进行权限配置。

![](/imgs/guide/team_permissions/team_roles_permissions/image3.png)

对于应用和知识库等资源，可直接修改成员权限。

![](/imgs/guide/team_permissions/team_roles_permissions/image4.png)

团队权限在专门的权限页面进行设置

![](/imgs/guide/team_permissions/team_roles_permissions/image5.png)

## 特殊权限说明

### 管理员权限

管理员主要负责管理资源的协作关系，但有以下限制：

- 不能修改或移除自身权限
- 不能修改或移除其他管理员权限
-不能将管理员权限赋予其他协作者

### Owner 权限

每个资源都有唯一的 Owner，拥有该资源的最高权限。Owner
可以转移所有权，但转移后原 Owner 将失去对资源的权限。

### Root 权限

Root
作为系统唯一的超级管理员账号，对所有团队的所有资源拥有完全访问和管理权限。

## 使用技巧

### 1. 设置团队默认权限

利用\"全员群组\"可快速为整个团队设置基础权限。例如，为应用设置全员可访问权限。

**注意**：个人成员权限会覆盖全员组权限。例如，应用 A
设置了全员编辑权限，而用户 M 被单独设置为使用权限，则用户 M
只能使用而无法编辑该应用。

### 2. 批量权限管理

通过创建群组或组织，可以高效管理多用户的权限配置。先将用户添加到群组，再对群组整体授权。

### 开发者参考
> 以下内容面向开发者，如不涉及二次开发可跳过。

#### 权限设计原理

FastGPT 权限系统参考 Linux 权限设计，采用二进制方式存储权限位。权限位为
1 表示拥有该权限，为 0 表示无权限。Owner 权限特殊标记为全 1。

#### 权限表

权限信息存储在 MongoDB 的 resource_permissions 集合中，其主要字段包括：

- teamId: 团队标识
- tmbId/groupId/orgId: 权限主体(三选一)
- resourceType: 资源类型(team/app/dataset)
- permission: 权限值(数字)
- resourceId: 资源ID(团队资源为null)

系统通过这一数据结构实现了灵活而精确的权限控制。

对于这个表的 Schema 定义在 packages/service/support/permission/schema.ts
文件中。定义如下：
```typescript
export const ResourcePermissionSchema = new Schema({
  teamId: {
    type: Schema.Types.ObjectId,
    ref: TeamCollectionName
  },
  tmbId: {
    type: Schema.Types.ObjectId,
    ref: TeamMemberCollectionName
  },
  groupId: {
    type: Schema.Types.ObjectId,
    ref: MemberGroupCollectionName
  },
  orgId: {
    type: Schema.Types.ObjectId,
    ref: OrgCollectionName
  },
  resourceType: {
    type: String,
    enum: Object.values(PerResourceTypeEnum),
    required: true
  },
  permission: {
    type: Number,
    required: true
  },
  // Resrouce ID: App or DataSet or any other resource type.
  // It is null if the resourceType is team.
  resourceId: {
    type: Schema.Types.ObjectId
  }
});
```
