---
title: V4.8.14
description: FastGPT V4.8.14 更新说明
---

## 更新指南

### 1. 做好数据备份

### 2. 修改镜像

- 更新 FastGPT 镜像 tag: v4.8.14-fix
- 更新 FastGPT 商业版镜像 tag: v4.8.14 （fastgpt-pro镜像）
- Sandbox 镜像，可以不更新

milvus版本使用：v4.8.14-milvus-fix 镜像。

## 新功能预览

### 自动触发工作流

可以允许你配置用户加载对话时，自动触发一次工作流。可以用于一些 CRM 系统，可以快速的引导用户使用，无需等待用户主动触发。

| | |
| --- | --- |
| ![alt text](/imgs/image-8.png) | ![alt text](/imgs/image-9.png) |


## 完整更新内容

1. 新增 - 工作流支持进入聊天框/点击开始对话后，自动触发一轮对话。
2. 新增 - 重写 chatContext，对话测试也会有日志，并且刷新后不会丢失对话。
3. 新增 - 分享链接支持配置是否允许查看原文。
4. 新增 - 新的 doc2x 插件。
5. 新增 - 繁体中文。
6. 新增 - 分析链接和 chat api 支持传入自定义 uid。
7. 商业版新增 - 微软 oauth 登录
8. 优化 - 工作流 ui 细节。
9. 优化 - 应用编辑记录采用 diff 存储，避免浏览器溢出。
10. 优化 - 代码入口，增加 register 入口，无需等待首次访问才执行。
11. 优化 - 工作流检查，增加更多缺失值检查。
12. 优化 - 增加知识库训练最大重试次数限制。
13. 优化 - 图片路径问题和示意图任务
14. 优化 - Milvus description
15. 修复 - 分块策略，四级标题会被丢失。 同时新增了五级标题的支持。
16. 修复 - MongoDB 知识库集合唯一索引。
17. 修复 - 反选知识库引用后可能会报错。
18. 修复 - 简易模式转工作流，不是使用最新编辑记录进行转移。
19. 修复 - 表单输入的说明文字不显示。
20. 修复 - API 无法使用 base64 图片。
