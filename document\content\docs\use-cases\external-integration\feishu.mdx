---
title: 接入飞书机器人教程
description: FastGPT 接入飞书机器人教程
---

从 4.8.10 版本起，FastGPT 商业版支持直接接入飞书机器人，无需额外的 API。 

## 1. 申请飞书应用

开一个免费的测试企业更方便进行调试。

1. 在[飞书开放平台](https://open.feishu.cn/app)的开发者后台申请企业自建应用。

![图片](/imgs/feishu-bot-1.png)

添加一个**机器人**应用。

## 2. 在 FastGPT 新建发布渠道

在fastgpt中选择想要接入的应用，在 发布渠道 页面，新建一个接入飞书机器人的发布渠道，填写好基础信息。
   
![图片](/imgs/feishu-bot-2.png)

## 3. 获取应用的 App ID, App Secret 两个凭证

在飞书开放平台开发者后台，刚刚创建的企业自建应用中，找到 App ID 和 App Secret，填入 FastGPT 新建发布渠道的对话框里面。
   
![图片](/imgs/feishu-bot-3.png)

填入两个参数到 FastGPT 配置弹窗中。

![图片](/imgs/feishu-bot-4.png)

（可选）在飞书开放平台开发者后台，点击事件与回调 -> 加密策略 获取 Encrypt Key，并填入飞书机器人接入的对话框里面

![图片](/imgs/feishu-bot-5.png)

Encrypt Key 用于加密飞书服务器与 FastGPT 之间通信。
建议如果使用 Https 协议，则不需要 Encrypt Key。如果使用 Http 协议通信，则建议使用 Encrypt Key
Verification Token 默认生成的这个 Token 用于校验来源。但我们使用飞书官方推荐的另一种更为安全的校验方式，因此可以忽略这个配置项。
## 4. 配置回调地址

新建好发布渠道后，点击**请求地址**，复制对应的请求地址。

在飞书控制台，点击左侧的 `事件与回调` ，点击`配置订阅方式`旁边的编辑 icon，粘贴刚刚复制的请求地址到输入框中。
   
| | | |
| --- | --- | --- |
| ![图片](/imgs/feishu-bot-10.jpg) | ![图片](/imgs/feishu-bot-11.jpg) | ![图片](/imgs/feishu-bot-6.png) |

## 5. 配置机器人回调事件和权限

* 添加 `接收消息` 事件
   
在`事件与回调`页面，点击`添加事件`。

搜索`接收消息`，或者直接搜索 `im.message.receive_v1` ，找到`接收消息 v2.0`的时间，勾选上并点击`确认添加`。

添加事件后，增加两个权限：点击对应权限，会有弹窗提示添加权限，添加上图两个权限。

| | |
| --- | --- |
| ![图片](/imgs/feishu-bot-7.png) | ![图片](/imgs/feishu-bot-8.png) |

不推荐启用上图中的两个“历史版本”，而是使用新版本的权限。
- 若开启 “读取用户发给机器人的单聊消息”， 则单聊发送给机器人的消息将被送到 FastGPT
- 若开启 “接收群聊中@机器人消息事件”， 则群聊中@机器人的消息将被送到 FastGPT
- 若开启（不推荐开启）“获取群组中所有消息”，则群聊中所有消息都将被送到 FastGPT

## 6. 配置回复消息权限

在飞书控制台，点击左侧的 `权限管理` ，搜索框中输入`发消息`，找到`以应用的身份发消息`的权限，点击开通权限。

![](/imgs/feishu-bot-13.jpg)

## 7. 发布机器人

点击飞书控制台左侧的`版本管理与发布`，即可发布机器人。

![](/imgs/feishu-bot-12.jpg)

然后就可以在工作台里找到你的机器人啦。接下来就是把机器人拉进群组，或者单独与它对话。

![图片](/imgs/feishu-bot-9.png)

## FAQ

### 发送了消息，没响应

1. 检查飞书机器人回调地址、权限等是否正确。
2. 查看 FastGPT 对话日志，是否有对应的提问记录
3. 如果有记录，飞书没回应，则是没给机器人开权限。
4. 如果没记录，则可能是应用运行报错了，可以先试试最简单的机器人。（飞书机器人无法输入全局变量、文件、图片内容）

### 如何新开一个聊天记录

飞书机器人的聊天记录 chatId 包含几种来源：
1. 私聊聊天框
2. 飞书话题群中单个话题
3. 群组聊天中，由群 id+个人id 组成。

如果你想重置你的聊天记录，可以给机器人发送 `Reset` 消息（注意大小写），机器人会新开一个聊天记录。
