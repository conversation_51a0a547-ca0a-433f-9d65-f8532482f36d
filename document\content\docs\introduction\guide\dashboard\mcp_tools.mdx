---
title: MCP 工具集
description: 快速了解 MCP 工具接入 FastGPT
---

FastGPT v4.9.6 版本开始，新增了 MCP 工具集 这种新的应用类型，允许传入一个 MCP 的 SSE URL 来批量创建可被模型轻松调用的 MCP 工具，下面就来看下如何创建 MCP 工具并且让 AI 调用

## 创建一个 MCP 工具集

首先选择新建 MCP 工具集，以对接高德地图的 MCP Server 为例，[高德地图 MCP Server](https://lbs.amap.com/api/mcp-server/create-project-and-key)

需要获取到一个 MCP 地址，例 https://mcp.amap.com/sse?key=xxx

![创建 MCP tools](/imgs/mcp_tools1.png)

然后填入到弹窗中的对应位置，点击后面的解析，会解析出对应的一系列工具

这时再点击创建就能轻松创建 MCP 工具和 MCP 工具集

## 测试 MCP 工具

进入到 MCP 工具集内部，能够对每个单独的 MCP 工具进行调试

![测试 MCP tools](/imgs/mcp_tools3.png)

以 maps_weather 这个查询天气的工具为例，点击运行，可以看到能够获得杭州的具体天气


## 模型调用工具

### 调用单个工具

![调用单个工具](/imgs/mcp_tools4.png)

选中 maps_weather 和 maps_text_search 这两个工具为例，分别问 AI 两个问题，可以看到 AI 智能地调用了相应的工具获得了需要的信息，然后根据获得的信息回答

| | |
|---|---|
| ![](/imgs/mcp_tools5.png) | ![](/imgs/mcp_tools6.png) |

### 调用工具集

FastGPT 也支持调用整个 MCP 工具集，AI 会自动选取需要的工具执行，

点击 MCP 工具集，会添加一个工具集类型的节点，使用工具调用节点连接

| | |
|---|---|
| ![](/imgs/mcp_tools7.png) | ![](/imgs/mcp_tools8.png) |

可以看到 AI 同样智能调用了相应的工具，获得了需要的信息，然后根据获得的信息回答
