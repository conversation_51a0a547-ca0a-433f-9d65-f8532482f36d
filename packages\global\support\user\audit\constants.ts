import type { auditLogMap, adminAuditLogMap } from '../../../../web/support/user/audit/constants';

export enum AdminAuditEventEnum {
  ADMIN_LOGIN = 'ADMIN_LOGIN',
  ADMIN_UPDATE_SYSTEM_MODAL = 'ADMIN_UPDATE_SYSTEM_MODAL',
  ADMIN_SEND_SYSTEM_INFORM = 'ADMIN_SEND_SYSTEM_INFORM',
  ADMIN_ADD_USER = 'ADMIN_ADD_USER',
  ADMIN_UPDATE_USER = 'ADMIN_UPDATE_USER',
  ADMIN_UPDATE_TEAM = 'ADMIN_UPDATE_TEAM',
  ADMIN_ADD_PLAN = 'ADMIN_ADD_PLAN',
  ADMIN_UPDATE_PLAN = 'ADMIN_UPDATE_PLAN',
  ADMIN_FINISH_INVOICE = 'ADMIN_FINISH_INVOICE',
  ADMIN_UPDATE_SYSTEM_CONFIG = 'ADMIN_UPDATE_SYSTEM_CONFIG',
  ADMIN_CREATE_APP_TEMPLATE = 'ADMIN_CREATE_APP_TEMPLATE',
  ADMIN_UPDATE_APP_TEMPLATE = 'ADMIN_UPDATE_APP_TEMPLATE',
  ADMIN_DELETE_APP_TEMPLATE = 'ADMIN_DELETE_APP_TEMPLATE',
  ADMIN_SAVE_TEMPLATE_TYPE = 'ADMIN_SAVE_TEMPLATE_TYPE',
  ADMIN_DELETE_TEMPLATE_TYPE = 'ADMIN_DELETE_TEMPLATE_TYPE',
  ADMIN_CREATE_PLUGIN = 'ADMIN_CREATE_PLUGIN',
  ADMIN_UPDATE_PLUGIN = 'ADMIN_UPDATE_PLUGIN',
  ADMIN_DELETE_PLUGIN = 'ADMIN_DELETE_PLUGIN',
  ADMIN_CREATE_PLUGIN_GROUP = 'ADMIN_CREATE_PLUGIN_GROUP',
  ADMIN_UPDATE_PLUGIN_GROUP = 'ADMIN_UPDATE_PLUGIN_GROUP',
  ADMIN_DELETE_PLUGIN_GROUP = 'ADMIN_DELETE_PLUGIN_GROUP'
}

export enum AuditEventEnum {
  //Team
  LOGIN = 'LOGIN',
  CREATE_INVITATION_LINK = 'CREATE_INVITATION_LINK',
  JOIN_TEAM = 'JOIN_TEAM',
  CHANGE_MEMBER_NAME = 'CHANGE_MEMBER_NAME',
  KICK_OUT_TEAM = 'KICK_OUT_TEAM',
  RECOVER_TEAM_MEMBER = 'RECOVER_TEAM_MEMBER',
  CREATE_DEPARTMENT = 'CREATE_DEPARTMENT',
  CHANGE_DEPARTMENT = 'CHANGE_DEPARTMENT',
  DELETE_DEPARTMENT = 'DELETE_DEPARTMENT',
  RELOCATE_DEPARTMENT = 'RELOCATE_DEPARTMENT',
  CREATE_GROUP = 'CREATE_GROUP',
  DELETE_GROUP = 'DELETE_GROUP',
  ASSIGN_PERMISSION = 'ASSIGN_PERMISSION',
  //APP
  CREATE_APP = 'CREATE_APP',
  UPDATE_APP_INFO = 'UPDATE_APP_INFO',
  MOVE_APP = 'MOVE_APP',
  DELETE_APP = 'DELETE_APP',
  UPDATE_APP_COLLABORATOR = 'UPDATE_APP_COLLABORATOR',
  DELETE_APP_COLLABORATOR = 'DELETE_APP_COLLABORATOR',
  TRANSFER_APP_OWNERSHIP = 'TRANSFER_APP_OWNERSHIP',
  CREATE_APP_COPY = 'CREATE_APP_COPY',
  CREATE_APP_FOLDER = 'CREATE_APP_FOLDER',
  UPDATE_PUBLISH_APP = 'UPDATE_PUBLISH_APP',
  CREATE_APP_PUBLISH_CHANNEL = 'CREATE_APP_PUBLISH_CHANNEL',
  UPDATE_APP_PUBLISH_CHANNEL = 'UPDATE_APP_PUBLISH_CHANNEL',
  DELETE_APP_PUBLISH_CHANNEL = 'DELETE_APP_PUBLISH_CHANNEL',
  EXPORT_APP_CHAT_LOG = 'EXPORT_APP_CHAT_LOG',
  CREATE_EVALUATION = 'CREATE_EVALUATION',
  EXPORT_EVALUATION = 'EXPORT_EVALUATION',
  DELETE_EVALUATION = 'DELETE_EVALUATION',
  //Dataset
  CREATE_DATASET = 'CREATE_DATASET',
  UPDATE_DATASET = 'UPDATE_DATASET',
  DELETE_DATASET = 'DELETE_DATASET',
  MOVE_DATASET = 'MOVE_DATASET',
  UPDATE_DATASET_COLLABORATOR = 'UPDATE_DATASET_COLLABORATOR',
  DELETE_DATASET_COLLABORATOR = 'DELETE_DATASET_COLLABORATOR',
  TRANSFER_DATASET_OWNERSHIP = 'TRANSFER_DATASET_OWNERSHIP',
  EXPORT_DATASET = 'EXPORT_DATASET',
  CREATE_DATASET_FOLDER = 'CREATE_DATASET_FOLDER',
  //Collection
  CREATE_COLLECTION = 'CREATE_COLLECTION',
  UPDATE_COLLECTION = 'UPDATE_COLLECTION',
  DELETE_COLLECTION = 'DELETE_COLLECTION',
  RETRAIN_COLLECTION = 'RETRAIN_COLLECTION',
  //Data
  CREATE_DATA = 'CREATE_DATA',
  UPDATE_DATA = 'UPDATE_DATA',
  DELETE_DATA = 'DELETE_DATA',
  //SearchTest
  SEARCH_TEST = 'SEARCH_TEST',
  //Account
  CHANGE_PASSWORD = 'CHANGE_PASSWORD',
  CHANGE_NOTIFICATION_SETTINGS = 'CHANGE_NOTIFICATION_SETTINGS',
  CHANGE_MEMBER_NAME_ACCOUNT = 'CHANGE_MEMBER_NAME_ACCOUNT',
  PURCHASE_PLAN = 'PURCHASE_PLAN',
  EXPORT_BILL_RECORDS = 'EXPORT_BILL_RECORDS',
  CREATE_INVOICE = 'CREATE_INVOICE',
  SET_INVOICE_HEADER = 'SET_INVOICE_HEADER',
  CREATE_API_KEY = 'CREATE_API_KEY',
  UPDATE_API_KEY = 'UPDATE_API_KEY',
  DELETE_API_KEY = 'DELETE_API_KEY'
}

export type AuditEventParamsType = {
  [K in AuditEventEnum]: (typeof auditLogMap)[K]['params'];
};
export type AdminAuditEventParamsType = {
  [K in AdminAuditEventEnum]: (typeof adminAuditLogMap)[K]['params'];
};
