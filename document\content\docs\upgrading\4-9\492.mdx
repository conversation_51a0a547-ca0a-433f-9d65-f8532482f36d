---
title: V4.9.2
description: FastGPT V4.9.2 更新说明
---

## 更新指南

可直接升级v4.9.3，v4.9.2存在一个工作流数据类型转化错误。

### 1. 做好数据库备份

### 2. SSO 迁移

使用了 SSO 或成员同步的商业版用户，并且是对接`钉钉`、`企微`的，需要迁移已有的 SSO 相关配置:

参考：[SSO & 外部成员同步](/docs/introduction/guide/admin/sso)中的配置进行`sso-service`的部署和配置。

1. 先将原商业版后台中的相关配置项复制备份出来（以企微为例，将 AppId, Secret 等复制出来）再进行镜像升级。
2. 参考上述文档，部署 SSO 服务，配置相关的环境变量
3. 如果原先使用企微组织架构同步的用户，升级完镜像后，需要在商业版后台切换团队模式为“同步模式”

### 3. 配置参数变更

修改`config.json`文件中`systemEnv.pgHNSWEfSearch`参数名，改成`hnswEfSearch`。  
商业版用户升级镜像后，直接在后台`系统配置-基础配置`中进行变更。

### 4. 更新镜像

- 更新 FastGPT 镜像 tag: v4.9.2
- 更新 FastGPT 商业版镜像 tag: v4.9.2
- Sandbox 镜像，可以不更新
- AIProxy 镜像修改为: registry.cn-hangzhou.aliyuncs.com/labring/aiproxy:v0.1.4

## 重要更新

- 知识库导入数据 API 变更，增加`chunkSettingMode`,`chunkSplitMode`,`indexSize`可选参数，具体可参考 [知识库导入数据 API](/docs/introduction/development/openapi/dataset) 文档。

## 🚀 新增内容

1. 知识库分块优化：支持单独配置分块大小和索引大小，允许进行超大分块，以更大的输入 Tokens 换取完整分块。
2. 知识库分块增加自定义分隔符预设值，同时支持自定义换行符分割。
3. 外部变量改名：自定义变量。 并且支持在测试时调试，在分享链接中，该变量直接隐藏。
4. 集合同步时，支持同步修改标题。
5. 团队成员管理重构，抽离主流 IM SSO（企微、飞书、钉钉），并支持通过自定义 SSO 接入 FastGPT。同时完善与外部系统的成员同步。
6. 支持 `oceanbase` 向量数据库。填写环境变量`OCEANBASE_URL`即可。
7. 基于 mistral-ocr 的 PDF 解析示例。
8. 基于 miner-u 的 PDF 解析示例。

## ⚙️ 优化

1. 导出对话日志时，支持导出成员名。
2. 邀请链接交互。
3. 无 SSL 证书时复制失败，会提示弹窗用于手动复制。
4. FastGPT 未内置 ai proxy 渠道时，也能正常展示其名称。
5. 升级 nextjs 版本至 14.2.25。
6. 工作流节点数组字符串类型，自动适配 string 输入。
7. 工作流节点数组类型，自动进行 JSON parse 解析 string 输入。
8. AI proxy 日志优化，去除重试失败的日志，仅保留最后一份错误日志。
9. 个人信息和通知展示优化。
10. 模型测试 loading 动画优化。
11. 分块算法小调整：

- 跨处理符号之间连续性更强。
- 代码块分割时，用 LLM 模型上下文作为分块大小，尽可能保证代码块完整性。
- 表格分割时，用 LLM 模型上下文作为分块大小，尽可能保证表格完整性。

## 🐛 修复

1. 飞书和语雀知识库无法同步。
2. 渠道测试时，如果配置了模型自定义请求地址，会走自定义请求地址，而不是渠道请求地址。
3. 语音识别模型测试未启用的模型时，无法正常测试。
4. 管理员配置系统插件时，如果插件包含其他系统应用，无法正常鉴权。
5. 移除 TTS 自定义请求地址时，必须需要填 requestAuth 字段。
