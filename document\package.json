{"name": "fast", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbo", "start": "next start", "postinstall": "fumadocs-mdx"}, "dependencies": {"@orama/orama": "^3.1.11", "@orama/tokenizers": "^3.1.11", "fast-glob": "^3.3.3", "fs-extra": "^11.3.0", "fumadocs-core": "15.6.3", "fumadocs-mdx": "11.6.11", "fumadocs-ui": "15.6.3", "gray-matter": "^4.0.3", "lucide-react": "^0.525.0", "next": "15.3.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-responsive": "^10.0.1", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-mdx": "^3.1.0", "remark-stringify": "^11.0.0"}, "devDependencies": {"@content-collections/core": "^0.10.0", "@content-collections/next": "^0.2.6", "@tailwindcss/postcss": "^4.1.11", "@types/mdx": "^2.0.13", "@types/node": "24.0.13", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "zod": "^4.0.5"}}