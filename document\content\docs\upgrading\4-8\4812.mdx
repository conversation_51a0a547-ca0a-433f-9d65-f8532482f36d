---
title: V4.8.12(包含升级脚本)
description: FastGPT V4.8.12 更新说明
---

## 更新指南

### 1. 做好数据备份

### 2. 修改镜像

- 更新 FastGPT 镜像 tag: v4.8.12-fix
- 更新 FastGPT 管理端镜像 tag: v4.8.12 （fastgpt-pro镜像）
- Sandbox 镜像，可以不更新


### 3. 商业版执行初始化

从任意终端，发起 1 个 HTTP 请求。其中 `{{rootkey}}` 替换成环境变量里的 `rootkey`；`{{host}}` 替换成**FastGPT 域名**:

```bash
curl --location --request POST 'https://{{host}}/api/admin/init/4812' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

会初始化应用和知识库的成员组数据。

### 4. 重构 Milvus 数据

由于 js int64 精度丢失问题，之前私有化使用 milvus 或者 zilliz 的用户，如果存在数据精度丢失的问题，需要重构 Milvus 数据。（可以查看 dataset_datas 表中，indexes 中的 dataId 是否末尾精度丢失）。使用 PG 的用户不需要操作。

从任意终端，发起 1 个 HTTP 请求。其中 `{{rootkey}}` 替换成环境变量里的 `rootkey`；`{{host}}` 替换成**FastGPT 域名**。

```bash
curl --location --request POST 'https://{{host}}/api/admin/resetMilvus' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

## 更新说明

1. 新增 - 全局变量支持数字类型，支持配置默认值和部分输入框参数。
2. 新增 - 插件自定义输入，文本输入框、数字输入框、选择框、开关，默认都支持作为变量引用。
3. 新增 - FE_DOMAIN 环境变量，配置该环境变量后，上传文件/图片会补全后缀后得到完整地址。（可解决 docx 文件图片链接，有时模型会伪造图片域名）
4. 新增 - 工具调用支持使用交互节点
5. 新增 - Debug 模式支持输入全局变量
6. 新增 - chat OpenAPI 文档
7. 新增 - wiki 搜索插件
8. 新增 - Google 搜索插件
9. 新增 - 数据库连接和操作插件
10. 新增 - Cookie 隐私协议提示
11. 新增 - HTTP 节点支持 JSONPath 表达式
12. 新增 - 应用和知识库支持成员组配置权限
13. 优化 - 循环节点支持选择外部节点的变量
14. 优化 - Docx 文件读取中， HTML to Markdown 优化，提高速度和大幅度降低内存消耗。
15. 修复 - 文件后缀判断，去除 query 影响。
16. 修复 - AI 响应为空时，会造成 LLM  历史记录合并。
17. 修复 - 用户交互节点未阻塞流程。
18. 修复 - 新建 APP，有时候会导致空指针报错。
19. 修复 - 拥有多个循环节点时，错误运行。
20. 修复 - 循环节点中修改变量，无法传递。
21. 修复 - 非 stream 模式，嵌套子应用/插件执行时无法获取子应用响应。
22. 修复 - 数据分块策略，同时将每个 Markdown 独立分块。 
