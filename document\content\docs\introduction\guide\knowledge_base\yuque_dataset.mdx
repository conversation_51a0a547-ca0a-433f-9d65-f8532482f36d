---
title: 语雀文件库
description: FastGPT 语雀文件库功能介绍和使用方式
---

| | |
| --- | --- |
| ![alt text](/imgs/image-31.png) | ![alt text](/imgs/image-32.png) |

FastGPT v4.8.16 版本开始，商业版用户支持语雀文件库导入，用户可以通过配置语雀的 token 和 uid 来导入语雀文档库。目前处于测试阶段，部分交互有待优化。

## 1. 获取语雀的 token 和 uid

在语雀首页 - 个人头像 - 设置，可找到对应参数。

![alt text](/imgs/image-36.png)

参考下图获取 Token 和 User ID，注意给 Token 赋值权限：

| 获取 Token | 增加权限 | 获取 User ID |
| --- | --- | --- |
| ![alt text](/imgs/image-33.png) | ![alt text](/imgs/image-34.png) | ![alt text](/imgs/image-35.png) |

## 2. 创建知识库

使用上一步获取的 token 和 uid，创建知识库，选择语雀文件库类型，然后填入对应的参数，点击创建。

![alt text](/imgs/image-37.png)

![alt text](/imgs/image-31.png)

## 3. 导入文档

创建完知识库后，点击`添加文件`即可导入语雀的文档库，跟随引导即可。

语雀知识库支持定时同步功能，每天会不定时的扫描一次，如果文档有更新，则会进行同步，也可以进行手动同步。

![alt text](/imgs/image-38.png)
