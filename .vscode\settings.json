{
    "editor.formatOnSave": true,
    "editor.mouseWheelZoom": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "prettier.prettierPath": "node_modules/prettier",
    "typescript.tsdk": "node_modules/typescript/lib",
    "i18n-ally.localesPaths": [
        "packages/web/i18n",
    ],
    "i18n-ally.enabledParsers": [
        "json",
        "yaml",
        "js",
        "ts"
    ],
    "i18n-ally.keystyle": "flat",
    "i18n-ally.sortKeys": true,
    "i18n-ally.keepFulfilled": false,
    "i18n-ally.sourceLanguage": "zh-CN", // 根据此语言文件翻译其他语言文件的变量和内容
    "i18n-ally.displayLanguage": "zh-CN", // 显示语言
    "i18n-ally.namespace": true,
    "i18n-ally.pathMatcher": "{locale}/{namespaces}.json",
    "i18n-ally.extract.targetPickingStrategy": "most-similar-by-key",
    "i18n-ally.translate.engines": ["deepl","google"],
    "[typescript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "mdx.server.enable": true,
    "markdown.copyFiles.overwriteBehavior": "nameIncrementally",
    "markdown.copyFiles.destination": {
        "/document/content/docs/**/*": "${documentWorkspaceFolder}/document/public/imgs/"
    },
    "files.associations": {
        "*.mdx": "markdown"
    }
}