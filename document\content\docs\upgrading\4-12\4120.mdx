---
title: 'V4.12.0(包含升级脚本)'
description: 'FastGPT V4.12.0 更新说明'
---

## 更新指南

### 1. 更新镜像：

- 更新 FastGPT 镜像tag: v4.12.0
- 更新 FastGPT 商业版镜像tag: v4.12.0
- 更新 fastgpt-plugin 镜像 tag: v0.1.9
- mcp_server 无需更新
- Sandbox 无需更新
- AIProxy 无需更新

### 2. 修改环境变量

修改 FastGPT 商业版(fastgpt-pro) 环境变量：

```sh
# 文件阅读时的密钥，与 fastgpt 镜像中环境变量一致
FILE_TOKEN_KEY=filetokenkey
```

### 3. 执行升级脚本

该脚本仅需商业版用户执行。

从任意终端，发起 1 个 HTTP 请求。其中 `{{rootkey}}` 替换成环境变量里的 `rootkey`；`{{host}}` 替换成**FastGPT 域名**。

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv4120' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

**脚本功能**

1. 初始化团队成员的应用对话日志权限。

## 🚀 新增内容

1. 商业版支持应用日志数据看板。
2. 商业版支持简易对话页，可直接选择模型和预设工具进行聊天，无需进行应用搭建。
3. 对话页，增加团队应用快速切换。
4. 权限表调整，采用 Role 映射 Permission 模式。
5. 应用可单独分配对话日志查看权限。

## ⚙️ 优化

1. 优化 3 处存在潜在内存泄露的代码。
2. 优化工作流部分递归检查，避免无限递归。
3. 优化文档阅读 Worker，采用 ShareBuffer 避免数据拷贝。
4. 批量进行向量生成和入库，减少网络操作。
5. 知识库搜索，多 query 合并计算，减少数据库操作。
6. 选择知识库交互优化。
7. 登录页 UI 调整。
8. 工作流中，更严格检测工具集是否可被添加。
9. 对话日志导出，仅导出选中的表头，并修复部分表头无法导出的问题。

## 🐛 修复

1. Doc2x API 更新，导致解析失败。
2. 工作流中，团队应用目录也可以被加入工作流。
3. 工作流，数组选择器 UI 缺陷。
4. 成员同步存在权限未完成删除问题

## 🔨 工具更新

1. 系统工具可返回 citeLinks 响应值，从而在对话框实现引用链接展示。