---
title: 接入钉钉机器人教程
description: FastGPT 接入钉钉机器人教程
---

从 4.8.16 版本起，FastGPT 商业版支持直接接入钉钉机器人，无需额外的 API。

## 1. 创建钉钉企业内部应用

1. 在[钉钉开发者后台](https://open-dev.dingtalk.com/fe/app)创建企业内部应用。

![图片1](/imgs/dingtalk-bot-1.png)

2. 获取**Client ID**和**Client Secret**。

![图片2](/imgs/dingtalk-bot-2.png)

## 2. 为 FastGPT 添加发布渠道

在 FastGPT 中选择要接入的应用，在**发布渠道**页面，新建一个接入钉钉机器人的发布渠道。

将前面拿到的 **Client ID** 和 **Client Secret** 填入配置弹窗中。

![图片3](/imgs/dingtalk-bot-3.png)

创建完成后，点击**请求地址**按钮，然后复制回调地址。

## 3. 为应用添加**机器人**应用能力。

在钉钉开发者后台，点击左侧**添加应用能力**，为刚刚创建的企业内部应用添加 **机器人** 应用能力。

![图片4](/imgs/dingtalk-bot-4.png)

## 4. 配置机器人回调地址

点击左侧**机器人** 应用能力，然后将底部**消息接受模式**设置为**HTTP模式**，消息接收地址填入前面复制的 FastGPT 的回调地址。

![图片5](/imgs/dingtalk-bot-5.png)

调试完成后，点击**发布**。

## 5. 发布应用
机器人发布后，还需要在**版本管理与发布**页面发布应用版本。

![图片6](/imgs/dingtalk-bot-6.png)

点击**创建新版本**后，设置版本号和版本描述后点击保存发布即可。

![图片7](/imgs/dingtalk-bot-7.png)

应用发布后，即可在钉钉企业中使用机器人功能，可对机器人私聊。或者在群组添加机器人后`@机器人`，触发对话。

![图片8](/imgs/dingtalk-bot-8.png)


## FAQ

### 如何新开一个聊天记录

如果你想重置你的聊天记录，可以给机器人发送 `Reset` 消息（注意大小写），机器人会新开一个聊天记录。
