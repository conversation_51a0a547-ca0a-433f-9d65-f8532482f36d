---
title: V4.9.11(包含升级脚本)
description: FastGPT V4.9.11 更新说明
---

## 更新指南

### 1. 更新镜像：

- 更新 FastGPT 镜像 tag: v4.9.11
- 更新 FastGPT 商业版镜像 tag: v4.9.11
- mcp_server 无需更新
- 更新 Sandbox 镜像 tag: v4.9.11
- AIProxy 无需更新

### 2. 执行升级脚本

该脚本仅需商业版用户执行。

从任意终端，发起 1 个 HTTP 请求。其中 `{{rootkey}}` 替换成环境变量里的 `rootkey`；`{{host}}` 替换成**FastGPT 域名**。

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv4911' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

**脚本功能**

1. 移动第三方知识库 API 配置。

## 🚀 新增内容

1. 商业版支持图片知识库。
2. 工作流中增加节点搜索功能。
3. 工作流中，子流程版本控制，可选择“保持最新版本”，无需手动更新。
4. 增加更多审计操作日志。
5. 知识库增加文档解析异步队列，导入文档时，无需等待文档解析完毕才进行导入。
6. 第三方知识库开发文档, [点击查看](/docs/introduction/guide/knowledge_base/third_dataset/)

## ⚙️ 优化

1. 原文缓存改用 gridfs 存储，提高上限。
2. 增加知识库模板导入选项。

## 🐛 修复

1. 工作流中，管理员声明的全局系统工具，无法进行版本管理。
2. 工具调用节点前，有交互节点时，上下文异常。
3. 修复备份导入，小于 1000 字时，无法分块问题。
4. 自定义 PDF 解析，无法保存 base64 图片。
5. 非流请求，未进行 CITE 标记替换。
6. Python 沙盒存在隐藏风险。
7. curl 导入插件缺失确认按键
