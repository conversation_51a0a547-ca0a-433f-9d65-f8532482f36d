import { createTokenizer } from '@orama/tokenizers/mandarin';

export const enhancedTokenizer = () => {
  // 整词配置 - 需要保持完整的词汇
  const wholeWords = [
    // 产品相关
    'fastgpt',
    'FastGPT',
    '快速GPT',
    'Saas',
    'SaaS',
    '云服务',
    '社区版',
    '商业版',
    '开源版',
    '企业版',
    '专业版',
    '智能对话系统',
    'AI应用平台',
    '知识库问答系统',

    // 核心功能模块
    '知识库',
    '工作流',
    '应用构建',
    '对话管理',
    '可视化编排',
    '拖拽式设计',
    '零代码搭建',
    '低代码开发',
    '流程编排',

    // 工作流节点
    'AI对话',
    '知识库搜索',
    '问题分类',
    '内容提取',
    '用户选择',
    '表单输入',
    '文本编辑',
    '指定回复',
    '文档解析',
    'HTTP请求',
    '真假判断',
    '变量更新',
    '代码运行',
    '循环',
    '指代消解',
    '自定义反馈',
    '工具调用',

    // AI模型相关
    'AI',
    'Agent',
    'LLM',
    '大语言模型',
    'ChatGPT',
    'GPT-4',
    'GPT-3.5',
    'Claude',
    '文心一言',
    '通义千问',
    'DeepSeek',
    'Function Call',
    'Prompt',
    '提示词',
    '系统提示词',
    'Temperature',
    '温度参数',
    'Token',
    'OpenAI',
    'Anthropic',
    '百度',
    '阿里云',
    '智谱AI',
    'One-API',
    'AI-Proxy',
    'Ollama',
    'Xinference',

    // 技术术语
    'API',
    'OpenAPI',
    'SSO',
    'MCP',
    '向量数据库',
    '语义搜索',
    'embedding',
    'RAG',
    '检索增强生成',
    'PGVector',
    'Milvus',
    'MongoDB',
    'PostgreSQL',
    'Redis',
    'Docker',
    'Sealos',
    'Nginx',
    'Cloudflare',

    // 数据处理
    '数据集',
    'Dataset',
    '文档导入',
    '数据处理',
    '索引模型',
    '重排模型',
    '分块策略',
    '增强处理',
    '问答拆分',
    '向量化',
    '文本分割',
    '知识结构化',
    '自动索引',
    '图片标注',
    'OCR识别',

    // 集成相关
    'Webhook',
    '第三方集成',
    '企业微信',
    '钉钉',
    'DingTalk',
    '飞书',
    'Feishu',
    'Lark',
    '微信公众号',

    // 业务概念
    '沙盒',
    'Sandbox',
    '插件',
    'Plugin',
    '模板',
    '权限管理',
    '团队协作',
    '用户角色',
    '访问控制',
    '数据安全',
    '隐私保护',

    // 界面元素
    '工作台',
    'Dashboard',
    '调试预览',
    '发布分享',
    '对话窗口',
    '聊天界面',
    '引用展示',
    '猜你想问',
    '文件上传',
    '表单配置',
    '系统配置',
    '模型配置',
    '参数设置',
    '高级编排',
    '简易模式',
    '专业模式',

    // 搜索插件
    'Bing搜索',
    'Google搜索',
    'SearXNG搜索',
    'Doc2x插件',

    // 文件格式
    'PDF',
    'Word',
    'Excel',
    'CSV',
    'TXT',
    'Markdown',
    'JSON',

    // 部署运维
    '环境变量',
    'config.json',
    '配置文件',
    '负载均衡',
    '高可用',
    '容灾备份',
    '监控告警',
    '性能优化',
    '响应速度',
    '并发处理',
    '缓存机制',

    // 多语言
    'i18n',
    '国际化',
    '中文',
    '英文',
    '日文',
    '多语言支持',

    // 版本管理
    '版本更新',
    '升级指南',
    '更新日志',
    '兼容性',
    '迁移指南'
  ];

  // 同义词配置 - 为整词添加相关的同义词
  const synonymsMap: Record<string, string[]> = {
    // 产品版本相关
    开源版: ['社区版', '开源', '开源版', '免费版'],
    商业版: ['商业版', '付费版', '企业版', '专业版', '标准版', '高级版'],
    社区版: ['社区版', '开源版', '免费版'],
    企业版: ['企业版', '商业版', '付费版', '专业版'],

    // 核心功能
    知识库: ['知识库', '知识体系', '数据库', '文档库', '资料库', '信息库', '语料库'],
    工作流: ['工作流', '流程', '工作流程', '业务流程', '可视化流程', '流程图', '编排', '组合'],
    应用构建: ['应用构建', '应用搭建', '应用创建', '搭建', '构建', '创建应用'],
    对话管理: ['对话管理', '聊天管理', '会话管理', '对话控制'],

    // AI相关
    AI: ['AI', '人工智能', '机器学习', '深度学习', '智能', '智能化'],
    大语言模型: ['大语言模型', 'LLM', '语言模型', '大模型', '生成式AI', '对话模型'],
    Agent: ['Agent', '智能体', '代理', '智能助手', 'AI助理', '机器人'],
    提示词: ['提示词', 'Prompt', '指令', '命令', '提示', '指示'],

    // 技术概念
    API: ['API', '接口', '接口调用', '应用程序接口', '集成接口', '外部接口'],
    向量数据库: ['向量数据库', '向量存储', '向量索引', 'Vector Database'],
    语义搜索: [
      '语义搜索',
      '语义检索',
      '智能检索',
      '相似度搜索',
      '向量搜索',
      '检索系统',
      '搜索引擎'
    ],
    RAG: ['RAG', '检索增强生成', '检索增强', '知识检索'],

    // 数据处理
    数据集: ['数据集', 'Dataset', '数据源', '数据', '资料'],
    文档导入: ['文档导入', '文件导入', '数据导入', '批量导入', '上传文档'],
    数据处理: ['数据处理', '文档处理', '内容处理', '文件解析', '信息提取', '结构化处理'],
    向量化: ['向量化', 'embedding', '向量转换', '特征提取'],

    // 界面相关
    工作台: ['工作台', 'Dashboard', '控制台', '管理台', '操作台', '仪表板'],
    调试预览: ['调试预览', '预览', '调试', '测试', '试运行'],
    发布分享: ['发布分享', '发布', '分享', '部署', '上线'],

    // 插件相关
    插件: ['插件', 'Plugin', '扩展', '组件', '模块', '附加功能', '外部工具', '第三方工具'],
    搜索插件: ['搜索插件', '搜索工具', '搜索扩展', '网络搜索'],

    // 集成相关
    第三方集成: ['第三方集成', '外部集成', '平台集成', '系统对接', '接口集成'],
    企业微信: ['企业微信', '企微', 'WeWork'],
    钉钉: ['钉钉', 'DingTalk', '阿里钉钉'],
    飞书: ['飞书', 'Feishu', 'Lark', '字节飞书'],

    // 权限管理
    权限管理: ['权限管理', '访问控制', '用户权限', '权限控制', '授权管理'],
    团队协作: ['团队协作', '多人协作', '协同工作', '团队管理'],
    用户角色: ['用户角色', '角色管理', '权限角色', '用户权限'],

    // 安全相关
    数据安全: ['数据安全', '信息安全', '数据保护', '安全防护'],
    隐私保护: ['隐私保护', '数据隐私', '信息保护', '隐私安全'],
    访问控制: ['访问控制', '权限控制', '访问权限', '安全认证'],

    // 部署相关
    Docker: ['Docker', '容器', '容器化', '镜像部署'],
    环境变量: ['环境变量', '配置变量', '系统变量', '环境配置'],
    配置文件: ['配置文件', '配置', '设置文件', 'config'],

    // 性能相关
    性能优化: ['性能优化', '系统优化', '速度提升', '效率优化', '性能调优'],
    响应速度: ['响应速度', '响应时间', '处理速度', '执行速度'],
    并发处理: ['并发处理', '并发', '多线程', '高并发'],
    缓存机制: ['缓存机制', '缓存', '内存缓存', '数据缓存'],

    // 多语言
    国际化: ['国际化', 'i18n', '多语言', '本地化'],
    多语言支持: ['多语言支持', '多语言', '国际化支持', '语言切换'],

    // 版本管理
    版本更新: ['版本更新', '系统更新', '功能更新', '补丁更新', '版本升级'],
    升级指南: ['升级指南', '更新指南', '迁移指南', '升级说明'],
    更新日志: ['更新日志', '版本日志', '修改日志', '发布日志'],

    // 工作流节点
    AI对话: ['AI对话', 'AI聊天', '智能对话', '机器人对话'],
    知识库搜索: ['知识库搜索', '文档搜索', '资料搜索', '内容搜索'],
    问题分类: ['问题分类', '意图识别', '分类器', '问题识别'],
    内容提取: ['内容提取', '信息提取', '文本提取', '数据提取'],
    用户选择: ['用户选择', '选择器', '用户输入', '交互选择'],
    表单输入: ['表单输入', '用户输入', '数据输入', '信息收集'],
    文档解析: ['文档解析', '文件解析', '内容解析', '格式转换'],
    代码运行: ['代码运行', '代码执行', '脚本执行', '沙盒执行'],
    工具调用: ['工具调用', '函数调用', 'Function Call', 'API调用']
  };

  const baseTokenizer = createTokenizer();

  return {
    ...baseTokenizer,
    tokenize: (text: string) => {
      // 先处理整词，用特殊标记保护它们
      let processedText = text;
      const protectedTokens = new Map();
      let tokenIndex = 0;

      wholeWords.forEach((word) => {
        const regex = new RegExp(`\\b${word}\\b`, 'gi');
        processedText = processedText.replace(regex, (match) => {
          const placeholder = `__PROTECTED_TOKEN_${tokenIndex}__`;
          protectedTokens.set(placeholder, match.toLowerCase());
          tokenIndex++;
          return placeholder;
        });
      });

      // 用基础分词器处理
      let tokens = baseTokenizer.tokenize(processedText);

      // 恢复被保护的整词，并添加同义词
      tokens = tokens
        .map((token) => {
          if (protectedTokens.has(token)) {
            const originalWord = protectedTokens.get(token);
            return synonymsMap[originalWord] || [originalWord];
          }
          return token;
        })
        .flat();

      return [...new Set(tokens)]; // 去重
    }
  };
};
