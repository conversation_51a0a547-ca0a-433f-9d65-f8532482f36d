---
title: V4.9.12
description: FastGPT V4.9.12 更新说明
---

## 更新指南

### 1. 更新环境变量

在 `fastgpt`和`fastgpt-pro`镜像环境变量中加入: `AES256_SECRET_KEY=` 变量，用于密钥加密。

### 2. 更新镜像：

- 更新 FastGPT 镜像 tag: v4.9.12
- 更新 FastGPT 商业版镜像 tag: v4.9.12
- mcp_server 无需更新
- Sandbox 无需更新
- 更新 AIProxy 镜像 tag: v0.2.2

## 🚀 新增内容

1. AI proxy 监控完善，支持以图表/表格形式查看模型调用和性能情况。
2. HTTP 节点和 MCP 支持单独“鉴权配置”，鉴权配置明文不会二次返回客户端，以保障数据安全。
3. 问题分类和内容提取，提示词中自动加入上一轮结果进行额外引导。
4. 判断器支持变量引用。
5. 商业版支持知识库分块时，LLM 进行自动分段识别。
6. Admin 管理员数据看板。
7. 豆包 1.6 系列模型，更新 qwen 模型配置。

## ⚙️ 优化

1. 密码校验时，增加更多的特殊字符
2. 后端全量计算知识库 chunk 参数，避免自动模式下部分参数未正确使用默认值。
3. 将文本分块移至 worker 线程，避免阻塞。
4. 展示更多套餐用量信息。
5. 优化输入框样式，桌面和移动端的语音输入样式更新。
6. MCP 工具调用，使用 Raw schema 进行工具调用，保障完整性。
7. 删除知识库文件时，如果文件不存在，不会阻断删除。
8. 升级 MCP SDK，兼容最新的 HTTPStreamable。
9. 语雀文档库，递归获取文档类型目录下的数据。

## 🐛 修复

1. 自定义问答提取提示词被覆盖。
2. 模板导入时，存在空 indexes 时，导致数据插入失败。
3. 登录页可能存在的 XSS 攻击。
4. 输入框语音输入时候会丢失文件列表的问题。
5. 知识库文档中图片 TTL 字段未清除，导致图片过期。
6. MCP 工具存储时，未转义 int 类型数据。
