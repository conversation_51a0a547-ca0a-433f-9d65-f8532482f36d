# Dependencies
node_modules
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env
.env.local
.env.production.local
.env.test.local

# Vercel
.vercel

# Typescript
*.tsbuildinfo

# Next.js
.next/
out/

# Production
build

# Misc
.DS_Store
*.pem

# Debug
debug.log*

# Git
.git
.gitignore

# Testing
coverage
.nyc_output

# Cache
.cache
.parcel-cache

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# IDE
.vscode
.idea

# OS
Thumbs.db

# Temporary folders
tmp/
temp/ 