---
title: V4.8.22(包含升级脚本)
description: FastGPT V4.8.22 更新说明
---

## 🌟更新指南

### 1. 做好数据库备份

### 2. 更新镜像：

- 更新 fastgpt 镜像 tag: v4.8.22
- 更新 fastgpt-pro 商业版镜像 tag: v4.8.22
- Sandbox 镜像无需更新

### 3. 运行升级脚本

仅商业版，并提供 Saas 服务的用户需要运行该升级脚本。

从任意终端，发起 1 个 HTTP 请求。其中 `{{rootkey}}` 替换成环境变量里的 `rootkey`；`{{host}}` 替换成**FastGPT 域名**:

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv4822' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

会迁移联系方式到对应用户表中。

## 🚀 新增内容

1. AI 对话节点解析 `<think></think>` 标签内容作为思考链，便于各类模型进行思考链输出。需主动开启模型输出思考。
2. 对话 API 优化，无论是否传递 chatId，都会保存对话日志。未传递 chatId，则随机生成一个 chatId 来进行存储。
3. ppio 模型提供商

## ⚙️ 优化

1. 模型未配置时提示，减少冲突提示。
2. 使用记录代码。
3. 内容提取节点，字段描述过长时换行。同时修改其输出名用 key，而不是 description。
4. 团队管理交互。
5. 对话接口，非流响应，增加报错字段。

## 🐛 修复

1. 思考内容未进入到输出 Tokens.
2. 思考链流输出时，有时与正文顺序偏差。
3. API 调用工作流，如果传递的图片不支持 Head 检测时，图片会被过滤。已增加该类错误检测，避免被错误过滤。
4. 模板市场部分模板错误。
5. 免登录窗口无法正常判断语言识别是否开启。
6. 对话日志导出，未兼容 sub path。
7. 切换团队时未刷新成员列表
8. list 接口在联查 member 时，存在空指针可能性。 
9. 工作流基础节点无法升级。
10. 向量检索结果未去重。
11. 用户选择节点无法正常连线。
12. 对话记录保存时，source 未正常记录。
