---
title: V4.6.3(包含升级脚本)
description: FastGPT V4.6.3
---

## 1。执行初始化 API

发起 1 个 HTTP 请求 (`{{rootkey}}` 替换成环境变量里的 `rootkey`，`{{host}}` 替换成自己域名)

1. https://xxxxx/api/admin/initv463

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv463' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

初始化说明：
1. 初始化Mongo 中 dataset，collection 和 data 的部分字段

## V4.6.3 功能介绍

1. 商业版新增 - web站点同步
2. 新增 - 集合元数据记录
3. 优化 - url 读取内容
4. 优化 - 流读取文件，防止内存溢出
5. 优化 - 4v模型自动将 url 转 base64，本地也可调试
6. 优化 - 图片压缩等级
7. 修复 - 图片压缩失败报错，防止文件读取过程卡死。
