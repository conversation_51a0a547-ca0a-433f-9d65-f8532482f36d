---
title: V4.9.7
description: FastGPT V4.9.7 更新说明
---

## 升级指南

### 1. 做好数据备份

### 2. 更新镜像 tag

- 更新 FastGPT 镜像 tag: v4.9.7-fix2
- 更新 FastGPT 商业版镜像 tag: v4.9.7
- mcp_server 无需更新
- Sandbox 无需更新
- 更新 AIProxy 镜像 tag: v0.1.8

## 🚀 新增内容

1. 知识库回答时，回答段落末尾增加引用。
2. MCP 工具支持 HTTP Streamable 协议。
3. MCP server 支持编辑工具名，适配部分客户端不支持中文名问题。
4. 工作流右键可自动对齐节点。
5. 支持生产环境自定义`config.json`路径。
6. API 调用，支持传递一个特殊 chatId(`NO_RECORD_HISTORIES`)，使得系统不会进行历史记录存储。
7. 支持 Rerank 模型按量计费。
8. 套餐兑换码功能。
9. 支付宝支付。
10. 短链数据埋点。
11. 新增 Jina AI 模型系统配置。

## ⚙️ 优化

1. Doc2x 文档解析，增加报错信息捕获，增加超时时长。
2. 调整 PG vector 查询语句，强制使用向量索引。
3. 对话时间统计，准确返回工作流整体运行时间。
4. 从 ai_proxy 获取音频解析时长。
5. AI 模型 Token 值均优先采用 API usage，确保 tokens 值准确，若为空，则再采用 GPT3.5 的估算方式。
6. 优化对话日志 list 接口，适配单个对话框，大量对话的场景。

## 🐛 修复

1. 文件上传分块大小限制，避免超出 MongoDB 限制。
2. 使用记录仪表盘，无法获取指定成员的使用统计。
3. 仪表盘接口，因未考虑时区问题，统计异常。
4. LLM 模型测试接口，无法测试未启用的 LLM。同时修复，模型测试接口会把模型自定义请求地址去除问题。
5. Copy app 权限问题。
6. 导出对话记录，限制单条对话记录消息上限 1000 组，避免导出失败。
7. 工作流变量下一段文本仍是工作流变量，不触发渲染。
8. 调试知识库检索模块，提示无权操作知识库。
9. 文本内容提取节点，默认值赋值逻辑。
10. 分享链接中，会强制返回嵌套应用中的引用内容。
11. 知识库集合元数据过滤时，不同知识库的同名标签使用 $and 筛选无法获取结果。
12. 修复应用列表，权限配置可能出现 index 刷新问题。

