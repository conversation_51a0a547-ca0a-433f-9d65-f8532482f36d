---
title: V4.8
description: FastGPT V4.8 更新说明
---

import { Alert } from '@/components/docs/Alert';

## 新工作流

FastGPT workflow V2上线，支持更加简洁的工作流模式。


<Alert icon="🤖" context="success">
**由于工作流差异较大，不少地方需要手动重新构建。请依次重建插件和应用**

简易尽快更新工作流，避免未来持续迭代后导致无法兼容。
</Alert>


给应用和插件增加了 version 的字段，用于标识是旧工作流还是新工作流。当你更新 4.8 后，保存和新建的工作流均为新版，旧版工作流会有一个重置的弹窗提示。并且，如果是通过 API 和 分享链接 调用的工作流，仍可以正常使用，直到你下次保存它们。

## 商业版配置更新

商业版用户如果配置了邮件验证码，需要在管理端 -> 项目配置 -> 登录配置 -> 邮箱登录配置 -> 修改 **邮箱服务SMTP地址**，之前只能配置别名，现在可以配置自定义的地址。下面是一组别名和实际地址关系：

qq: smtp.qq.com
gmail: smtp.gmail.com

## V4.8 更新说明

1. 重构 - 工作流
2. 新增 - 判断器。支持 if elseIf else 判断。 @newfish-cmyk  （preview版本的if else节点需要删除重建）
3. 新增 - 变量更新节点。支持更新运行中工作流输出变量，或更新全局变量。@newfish-cmyk 
4. 新增 - 工作流自动保存和版本管理。
5. 新增 - 工作流 Debug 模式，可以调试单个节点或者逐步调试工作流。
6. 新增 - 定时执行应用。可轻松实现定时任务。
7. 新增 - 插件自定义输入优化，可以渲染输入组件。
8. 新增 - 分享链接发送对话前 hook https://github.com/labring/FastGPT/pull/1252 @gaord 
9. 优化 - 工作流连线，可以四向连接，方便构建循环工作流。
10. 优化 - 工作流上下文传递，性能🚀。
11. 优化 - ctrl和alt+enter换行，换行符位置不正确。
12. 优化 - chat中存储变量配置。避免修改变量后，影响旧的对话。
13. 优化 - 简易模式，更新配置后自动更新调试框内容，无需保存。
14. 优化 - worker进程管理，并将计算 Token 任务分配给 worker 进程。
15. 优化 - 工具调用支持指定字段数据类型（string, boolean, number） https://github.com/labring/FastGPT/issues/1236
16. 优化 - completions接口size限制 https://github.com/labring/FastGPT/issues/1241
17. 优化 - Node api 中间件。优化 api 端代码。@c121914yu 
18. 优化 - 对话记录保持为偶数进行截取，避免部分模型不支持奇数的历史记录，最大长度增加到50轮。 https://github.com/labring/FastGPT/issues/1384
19. 优化 - HTTP节点错误后终止进程 https://github.com/labring/FastGPT/issues/1290
20. 修复 - 工具调用时候，name不能是数字开头（随机数有概率数字开头）@c121914yu 
21. 修复 - 分享链接， query 全局变量会被缓存。  @c121914yu 
22. 修复 - 工具调用字段兼容。 https://github.com/labring/FastGPT/issues/1253
23. 修复 - HTTP 模块url光标问题 https://github.com/labring/FastGPT/issues/1334 @maquannene 
