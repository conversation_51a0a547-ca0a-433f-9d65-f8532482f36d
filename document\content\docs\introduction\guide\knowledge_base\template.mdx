---
title: 模板导入
description: FastGPT 模板导入功能介绍和使用方式
---

## 背景

FastGPT 提供了模板导入功能，让用户可以通过预设的 CSV 模板格式批量导入问答对数据。这种方式特别适合已经有结构化问答数据的用户，可以快速将数据导入到知识库中。

## 模板结构说明

模板采用 CSV 格式，包含以下列：

- q: 问题列，存放用户可能提出的问题
- a: 答案列，存放对应问题的标准答案
- indexes: 索引列，用于存放与该问题相关的索引

### 示例数据

```csv
q,a,indexes
"你是谁呀？","我呀，是 AI 小助手哟，专门在这儿随时准备着，陪你交流、为你答疑解惑，不管是学习上的知识探讨，生活里的小疑问，还是创意灵感的碰撞，我都能尽力参与，用我的 "知识大脑" 给你提供帮助和陪伴呢，就盼着能成为你互动交流、探索世界的好伙伴呀 。","1. 你是什么？\n2. 你能做什么？\n3. 你可以解答哪些方面的疑问？\n4. 你希望成为什么样的伙伴？\n5. 你如何提供帮助？"
"你是什么？","我是 AI 小助手，专门随时准备陪用户交流、为用户答疑解惑，能参与学习上的知识探讨、生活里的小疑问以及创意灵感的碰撞，用 "知识大脑" 提供帮助和陪伴，希望成为用户互动交流、探索世界的好伙伴。","你是什么？"
"你能做什么？","能陪用户交流、为用户答疑解惑，参与学习上的知识探讨、生活里的小疑问以及创意灵感的碰撞，用 "知识大脑" 提供帮助和陪伴。","你能做什么？"
```

## 使用说明

### 1. 打开知识库，点击导入，选择模版导入

![](/imgs/template/import.png)

![](/imgs/template/box.png)

### 2. 下载模板

点击下载 CSV 模版，其中存在两个模式的内容

#### 常规模式的数据模版

![](/imgs/template/nomal.png)

对应 CSV 格式为

![](/imgs/template/nomal_data.png)

常规模式下，q为内容，a为空，indexes可多个

#### 问答对的数据模版

![](/imgs/template/Question-answer.png)

对应CSV格式为

![](/imgs/template/Question-answer_data.png)

问答对模式下，q为问题，a为答案，indexes即为索引部分

### 3. 填写数据

按照模板格式填写你的问答数据：
- 每一行代表一个内容或者一个问答对
- 问题(q)始终不为空
- 在一行内，索引部分可往后继续添加

### 4. 导入限制

- 仅支持 CSV 格式文件
- 单个文件大小限制为 100MB
- 必须严格按照模板格式填写，否则可能导入失败
- 每次只能导入一个文件

成功导入后如下：

![](/imgs/template/import_csv.png)

### 4. 注意事项

- 确保 CSV 文件使用 UTF-8 编码
- 如果内容中包含逗号，请用双引号包裹整个内容
- indexes 列的内容会被用作相关问题的索引，有助于提高检索准确性
- 建议在导入大量数据前先测试少量数据

## 最佳实践

1. **数据准备**
   - 确保内容或者问答对的质量，答案应该清晰、准确
   - 为每个导入的添加合适的索引关键词
   - 避免重复的内容或者问答对

2. **格式检查**
   - 导入前检查 CSV 文件格式是否正确
   - 确保没有多余的空行或空格
   - 验证特殊字符是否正确转义

3. **分批导入**
   - 如果数据量较大，建议分批导入
   - 每批导入后验证数据的正确性

## 常见问题

Q: 为什么我的文件导入失败了？
A: 请检查以下几点：
- 文件格式是否为 CSV
- 编码是否为 UTF-8
- 是否严格按照模板格式填写
- 文件大小是否超过限制

Q: 如何验证导入是否成功？
A: 导入成功后，你可以：
- 在知识库中搜索导入的问题
- 通过对话测试回答的准确性
- 查看知识库的数据统计 
