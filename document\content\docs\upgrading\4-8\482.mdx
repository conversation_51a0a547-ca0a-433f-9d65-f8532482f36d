---
title: V4.8.2
description: FastGPT V4.8.2 更新说明
---

## Sealos 升级说明

1. 在应用管理中新建一个应用，镜像为：registry.cn-hangzhou.aliyuncs.com/fastgpt/fastgpt-sandbox:v4.8.1
2. 无需外网访问地址，端口号为3000
3. 部署完后，复制应用的内网地址
4. 点击变更`FastGPT - 修改环境变量，增加下面的环境变量即可

```
SANDBOX_URL=内网地址
```

## Docker 部署

可以拉取最新 [docker-compose.yml](https://github.com/labring/FastGPT/blob/main/deploy/docker/docker-compose.yml) 文件参考

1. 新增一个容器 `sandbox`
2. fastgpt 和 fastgpt-pro(商业版) 容器新增环境变量: `SANDBOX_URL`
3. sandbox 简易不要开启外网访问，未做凭证校验。

## V4.8.2 更新说明

1. 新增 - js代码运行节点（更完整的type提醒，后续继续完善）
2. 新增 - 内容提取节点支持数据类型选择
3. 修复 - 新增的站点同步无法使用
4. 修复 - 定时任务无法输入内容
