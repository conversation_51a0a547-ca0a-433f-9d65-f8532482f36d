name: 'FastGPT-Test'
on:
  pull_request:
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest

    permissions:
      # Required to checkout the code
      contents: read
      # Required to put a comment into the pull-request
      pull-requests: write

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name }}
      - uses: pnpm/action-setup@v4
        with:
          version: 10
      - name: 'Install Deps'
        run: pnpm install
      - name: 'Test'
        run: pnpm run test
      - name: 'Report Coverage'
        # Set if: always() to also generate the report if tests are failing
        # Only works if you set `reportOnFailure: true` in your vite config as specified above
        if: always()
        uses: davelosert/vitest-coverage-report-action@v2
