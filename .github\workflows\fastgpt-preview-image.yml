name: Preview FastGPT images
on:
  pull_request_target:
  workflow_dispatch:

jobs:
  preview-fastgpt-images:
    permissions:
      contents: read
      packages: write
      attestations: write
      id-token: write
      pull-requests: write

    runs-on: ubuntu-24.04
    strategy:
      matrix:
        image: [fastgpt, sandbox, mcp_server]
      fail-fast: false # 即使一个镜像构建失败，也继续构建其他镜像

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name }}
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          driver-opts: network=host

      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}-${{ matrix.image }}
          restore-keys: |
            ${{ runner.os }}-buildx-${{ github.sha }}-
            ${{ runner.os }}-buildx-

      - name: <PERSON><PERSON> to Aliyun Container Registry
        uses: docker/login-action@v3
        with:
          registry: registry.cn-hangzhou.aliyuncs.com
          username: ${{ secrets.ALI_HUB_USERNAME }}
          password: ${{ secrets.ALI_HUB_PASSWORD }}

      - name: Set image config
        id: config
        run: |
          if [[ "${{ matrix.image }}" == "fastgpt" ]]; then
            echo "DOCKERFILE=projects/app/Dockerfile" >> $GITHUB_OUTPUT
            echo "DESCRIPTION=fastgpt-pr image" >> $GITHUB_OUTPUT
            echo "DOCKER_REPO_TAGGED=${{ secrets.ALI_IMAGE_NAME }}/fastgpt-pr:fatsgpt_${{ github.event.pull_request.head.sha }}" >> $GITHUB_OUTPUT
          elif [[ "${{ matrix.image }}" == "sandbox" ]]; then
            echo "DOCKERFILE=projects/sandbox/Dockerfile" >> $GITHUB_OUTPUT
            echo "DESCRIPTION=fastgpt-sandbox-pr image" >> $GITHUB_OUTPUT
            echo "DOCKER_REPO_TAGGED=${{ secrets.ALI_IMAGE_NAME }}/fastgpt-pr:fatsgpt_sandbox_${{ github.event.pull_request.head.sha }}" >> $GITHUB_OUTPUT
          elif [[ "${{ matrix.image }}" == "mcp_server" ]]; then
            echo "DOCKERFILE=projects/mcp_server/Dockerfile" >> $GITHUB_OUTPUT
            echo "DESCRIPTION=fastgpt-mcp_server-pr image" >> $GITHUB_OUTPUT
            echo "DOCKER_REPO_TAGGED=${{ secrets.ALI_IMAGE_NAME }}/fastgpt-pr:fatsgpt_mcp_server_${{ github.event.pull_request.head.sha }}" >> $GITHUB_OUTPUT
          fi

      - name: Build ${{ matrix.image }} image for PR
        run: |
          docker buildx build \
          -f ${{ steps.config.outputs.DOCKERFILE }} \
          --label "org.opencontainers.image.source=https://github.com/${{ github.repository_owner }}/FastGPT" \
          --label "org.opencontainers.image.description=${{ steps.config.outputs.DESCRIPTION }}" \
          --push \
          --cache-from=type=local,src=/tmp/.buildx-cache \
          --cache-to=type=local,dest=/tmp/.buildx-cache \
          -t ${{ steps.config.outputs.DOCKER_REPO_TAGGED }} \
          .

      - name: '@finleyge/github-tools'
        uses: FinleyGe/github-tools@0.0.1
        id: print-image-label
        if: success()
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          tool: issue-comment
          title: 'Preview ${{ matrix.image }} Image:'
          body: |
            ```
            ${{ steps.config.outputs.DOCKER_REPO_TAGGED }}
            ```
