name: Build FastGPT images in Personal warehouse
on:
  workflow_dispatch:
  push:
    paths:
      - 'projects/app/**'
      - 'packages/**'
    branches:
      - 'main'

jobs:
  get-vars:
    runs-on: ubuntu-24.04
    outputs:
      docker_repo: ${{ steps.set_docker_repo.outputs.docker_repo }}
      docker_tag: ${{ steps.set_docker_repo.outputs.docker_tag }}
    steps:
      - name: Set docker repository and tag
        id: set_docker_repo
        run: |
          echo "docker_repo=ghcr.io/$(echo ${{ github.repository }} | tr '[:upper:]' '[:lower:]')" >> $GITHUB_OUTPUT
          if [[ "${{ github.ref_name }}" == "main" ]]; then
            echo "docker_tag=latest" >> $GITHUB_OUTPUT
          else
            echo "docker_tag=${{ github.ref_name }}" >> $GITHUB_OUTPUT
          fi

  build-fastgpt-images:
    needs: get-vars
    permissions:
      packages: write
      contents: read
      attestations: write
      id-token: write
    strategy:
      matrix:
        archs:
          - arch: amd64
            runs-on: ubuntu-24.04
          - arch: arm64
            runs-on: ubuntu-24.04-arm
    runs-on: ${{ matrix.archs.runs-on || 'ubuntu-24.04' }}
    if: github.repository != 'labring/FastGPT'
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: network=host
      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-${{ matrix.archs.arch }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.archs.arch }}-buildx-
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Build and push for ${{ matrix.archs.arch }}
        id: build
        uses: docker/build-push-action@v6
        with:
          context: .
          file: projects/app/Dockerfile
          platforms: linux/${{ matrix.archs.arch }}
          labels: |
            org.opencontainers.image.source=https://github.com/${{ github.repository }}
            org.opencontainers.image.description=fastgpt image
          outputs: type=image,"name=${{ needs.get-vars.outputs.docker_repo }}",push-by-digest=true,push=true
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache
      - name: Export digest
        run: |
          mkdir -p ${{ runner.temp }}/digests
          digest="${{ steps.build.outputs.digest }}"
          touch "${{ runner.temp }}/digests/${digest#sha256:}"

      - name: Upload digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-${{ github.sha }}-${{ matrix.archs.arch }}
          path: ${{ runner.temp }}/digests/*
          if-no-files-found: error
          retention-days: 1

  release-fastgpt-images:
    permissions:
      packages: write
      contents: read
      attestations: write
      id-token: write
    needs: [get-vars, build-fastgpt-images]
    runs-on: ubuntu-24.04
    steps:
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Download digests
        uses: actions/download-artifact@v4
        with:
          path: ${{ runner.temp }}/digests
          pattern: digests-${{ github.sha }}-*
          merge-multiple: true

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Set image name and tag
        run: |
          echo "Git_Tag=${{ needs.get-vars.outputs.docker_repo }}:${{ needs.get-vars.outputs.docker_tag }}" >> $GITHUB_ENV
          echo "Git_Latest=${{ needs.get-vars.outputs.docker_repo }}:latest" >> $GITHUB_ENV
    
      - name: Create manifest list and push
        working-directory: ${{ runner.temp }}/digests
        run: |
          TAGS="$(echo -e "${Git_Tag}\n${Git_Latest}")"
          for TAG in $TAGS; do
            docker buildx imagetools create -t $TAG \
              $(printf '${{ needs.get-vars.outputs.docker_repo }}@sha256:%s ' *)
            sleep 5
          done
