---
title: V4.8.9（需要初始化）
description: FastGPT V4.8.9 更新说明
---

## 升级指南

### 1. 做好数据库备份

### 2. 修改镜像

- 更新 FastGPT 镜像 tag: v4.8.9
- 更新 FastGPT 商业版镜像 tag: v4.8.9
- Sandbox 镜像，可以不更新

### 3. 商业版执行初始化

从任意终端，发起 1 个 HTTP 请求。其中 `{{rootkey}}` 替换成环境变量里的 `rootkey`；`{{host}}` 替换成**FastGPT 域名**:

```bash
curl --location --request POST 'https://{{host}}/api/admin/init/489' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

会初始化多租户的通知方式，仅内部使用的，无需执行。

-------

## V4.8.9 更新说明

1. 新增 - 文件上传配置，不再依赖视觉模型决定是否可上传图片，而是通过系统配置决定。
2. 新增 - AI 对话节点和工具调用支持选择“是否开启图片识别”，开启后会自动获取对话框上传的图片和“用户问题”中的图片链接。
3. 新增 - 文档解析节点。
4. 商业版新增 - 团队通知账号绑定，用于接收重要信息。
5. 商业版新增 - 知识库集合标签功能，可以对知识库进行标签管理。
6. 商业版新增 - 知识库搜索节点支持标签过滤和创建时间过滤。
7. 商业版新增 - 转移 App owner 权限。
8. 新增 - 删除所有对话引导内容。
9. 新增 - QA 拆分支持自定义 chunk 大小，并优化 gpt4o-mini 拆分时，chunk 太大导致生成内容很少的问题。
10. 优化 - 对话框信息懒加载，减少网络传输。
11. 优化 - 清除选文件缓存，支持重复选择同一个文件。
12. 修复 - 知识库上传文件，网络不稳定或文件较多情况下，进度无法到 100%。
13. 修复 - 删除应用后回到聊天选择最后一次对话的应用为删除的应用时提示无该应用问题。
14. 修复 - 插件动态变量配置默认值时，无法正常显示默认值。
15. 修复 - 工具调用温度和最大回复值未生效。
16. 修复 - 函数调用模式，assistant role 中，GPT 模型必须传入 content 参数。（不影响大部分模型，目前基本都改用用 ToolChoice 模式，FC 模式已弃用）。
17. 修复 - 知识库文件上传进度更新可能异常。
18. 修复 - 知识库 rebuilding 时候，页面总是刷新到第一页。
19. 修复 - 知识库 list openapi 鉴权问题。
20. 修复 - 分享链接，新对话无法反馈。
